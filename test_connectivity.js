// Simple Node.js script to test frontend-backend connectivity
const axios = require('axios');

async function testConnectivity() {
    console.log('🔍 Testing Frontend-Backend Connectivity');
    console.log('=' * 50);

    // Test 1: Basic backend health check
    try {
        const healthResponse = await axios.get('http://localhost:8000/docs');
        console.log('✅ Backend server is reachable');
    } catch (error) {
        console.log('❌ Backend server not reachable:', error.message);
        return;
    }

    // Test 2: Test registration endpoint directly
    const testUser = {
        email: '<EMAIL>',
        username: 'connectivitytest',
        full_name: 'Connectivity Test',
        password: 'TestPass123!'
    };

    try {
        console.log('\n📝 Testing registration endpoint...');
        console.log('Request data:', testUser);

        const response = await axios.post(
            'http://localhost:8000/api/v1/auth/register',
            testUser,
            {
                headers: {
                    'Content-Type': 'application/json'
                }
            }
        );

        console.log('✅ Registration successful!');
        console.log('Response status:', response.status);
        console.log('Response data:', response.data);

    } catch (error) {
        console.log('❌ Registration failed');
        console.log('Error status:', error.response?.status);
        console.log('Error data:', error.response?.data);
        console.log('Error message:', error.message);

        if (error.response?.status === 400 && error.response?.data?.detail?.includes('already')) {
            console.log('ℹ️  User already exists - this is expected for repeated tests');
        }
    }

    // Test 3: Test with axios configuration similar to frontend
    try {
        console.log('\n🔧 Testing with frontend-like axios config...');
        
        const api = axios.create({
            baseURL: 'http://localhost:8000',
            headers: {
                'Content-Type': 'application/json',
            },
            withCredentials: false,
        });

        const testUser2 = {
            email: '<EMAIL>',
            username: 'connectivitytest2',
            full_name: 'Connectivity Test 2',
            password: 'TestPass123!'
        };

        const response = await api.post('/api/v1/auth/register', testUser2);
        
        console.log('✅ Frontend-like request successful!');
        console.log('Response status:', response.status);
        console.log('Response data:', response.data);

    } catch (error) {
        console.log('❌ Frontend-like request failed');
        console.log('Error status:', error.response?.status);
        console.log('Error data:', error.response?.data);
        console.log('Error message:', error.message);

        if (error.response?.status === 400 && error.response?.data?.detail?.includes('already')) {
            console.log('ℹ️  User already exists - this is expected for repeated tests');
        }
    }

    console.log('\n✅ Connectivity test completed!');
}

// Run the test
testConnectivity().catch(console.error);
