"""
Test suite for ETL Pipeline Builder functionality
Following TDD approach - writing tests first
"""
import unittest
from fastapi.testclient import TestClient
from datetime import datetime, timezone
import json

from main import app
from models.database import Base, engine, get_db
from models.models import User, Organization, DataSource, ETLPipeline, DataSourceType, PipelineStatus
from sqlalchemy.orm import Session

# Create test client
client = TestClient(app)

# Create test database tables
Base.metadata.create_all(bind=engine)


class TestETLPipelineBuilder(unittest.TestCase):
    """Test ETL Pipeline creation and management"""

    def setUp(self):
        """Set up test data before each test"""
        self.db = next(get_db())

        # Create test organization
        self.org = Organization(
            name="Test Org",
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(self.org)
        self.db.commit()

        # Create test data source
        self.data_source = DataSource(
            organization_id=self.org.id,
            name="Test Salesforce",
            source_type=DataSourceType.SALESFORCE,
            connection_config={"status": "connected"},
            is_active=True,
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(self.data_source)
        self.db.commit()

    def tearDown(self):
        """Clean up after each test"""
        self.db.query(ETLPipeline).delete()
        self.db.query(DataSource).delete()
        self.db.query(Organization).delete()
        self.db.commit()
        self.db.close()

    def test_create_etl_pipeline(self):
        """Test creating a new ETL pipeline"""
        pipeline_data = {
            "name": "Salesforce to DW Pipeline",
            "organization_id": self.org.id,
            "source_id": self.data_source.id,
            "destination_config": {
                "type": "data_warehouse",
                "connection_string": "postgresql://localhost/dw"
            },
            "transformation_rules": {
                "field_mapping": {
                    "lead_name": "customer_name",
                    "lead_email": "email"
                },
                "filters": [
                    {"field": "status", "operator": "equals", "value": "qualified"}
                ]
            },
            "schedule": "0 */6 * * *"  # Every 6 hours
        }

        response = client.post("/api/v1/etl/pipelines", json=pipeline_data)
        self.assertEqual(response.status_code, 201)

        data = response.json()
        self.assertIn("id", data)
        self.assertEqual(data["name"], "Salesforce to DW Pipeline")
        self.assertEqual(data["status"], "active")

    def test_list_pipelines(self):
        """Test listing all pipelines for an organization"""
        response = client.get(
            "/api/v1/etl/pipelines",
            params={"organization_id": self.org.id}
        )
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertIsInstance(data, list)

    def test_get_single_pipeline(self):
        """Test retrieving a single pipeline"""
        # First create a pipeline
        pipeline = ETLPipeline(
            organization_id=self.org.id,
            name="Test Pipeline",
            source_id=self.data_source.id,
            destination_config={"type": "data_warehouse"},
            transformation_rules={},
            schedule="0 0 * * *",
            status=PipelineStatus.ACTIVE,
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(pipeline)
        self.db.commit()

        response = client.get(f"/api/v1/etl/pipelines/{pipeline.id}")
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data["id"], pipeline.id)
        self.assertEqual(data["name"], "Test Pipeline")

    def test_update_pipeline(self):
        """Test updating pipeline configuration"""
        # Create pipeline first
        pipeline = ETLPipeline(
            organization_id=self.org.id,
            name="Original Pipeline",
            source_id=self.data_source.id,
            destination_config={"type": "data_warehouse"},
            transformation_rules={},
            schedule="0 0 * * *",
            status=PipelineStatus.ACTIVE,
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(pipeline)
        self.db.commit()

        update_data = {
            "name": "Updated Pipeline",
            "schedule": "0 */3 * * *"  # Every 3 hours
        }

        response = client.put(
            f"/api/v1/etl/pipelines/{pipeline.id}",
            json=update_data
        )
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data["name"], "Updated Pipeline")
        self.assertEqual(data["schedule"], "0 */3 * * *")

    def test_delete_pipeline(self):
        """Test deleting a pipeline"""
        # Create pipeline first
        pipeline = ETLPipeline(
            organization_id=self.org.id,
            name="Pipeline to Delete",
            source_id=self.data_source.id,
            destination_config={"type": "data_warehouse"},
            transformation_rules={},
            schedule="0 0 * * *",
            status=PipelineStatus.ACTIVE,
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(pipeline)
        self.db.commit()

        response = client.delete(f"/api/v1/etl/pipelines/{pipeline.id}")
        self.assertEqual(response.status_code, 200)

        # Verify it's deleted
        deleted_pipeline = self.db.query(ETLPipeline).filter(
            ETLPipeline.id == pipeline.id
        ).first()
        self.assertIsNone(deleted_pipeline)

    def test_run_pipeline_manually(self):
        """Test triggering a pipeline run manually"""
        # Create pipeline first
        pipeline = ETLPipeline(
            organization_id=self.org.id,
            name="Manual Run Pipeline",
            source_id=self.data_source.id,
            destination_config={"type": "data_warehouse"},
            transformation_rules={},
            schedule="0 0 * * *",
            status=PipelineStatus.ACTIVE,
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(pipeline)
        self.db.commit()

        response = client.post(f"/api/v1/etl/pipelines/{pipeline.id}/run")
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertIn("run_id", data)
        self.assertEqual(data["status"], "running")

    def test_pause_pipeline(self):
        """Test pausing an active pipeline"""
        # Create active pipeline
        pipeline = ETLPipeline(
            organization_id=self.org.id,
            name="Pipeline to Pause",
            source_id=self.data_source.id,
            destination_config={"type": "data_warehouse"},
            transformation_rules={},
            schedule="0 0 * * *",
            status=PipelineStatus.ACTIVE,
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(pipeline)
        self.db.commit()

        response = client.post(f"/api/v1/etl/pipelines/{pipeline.id}/pause")
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data["status"], "inactive")

    def test_resume_pipeline(self):
        """Test resuming a paused pipeline"""
        # Create paused pipeline
        pipeline = ETLPipeline(
            organization_id=self.org.id,
            name="Pipeline to Resume",
            source_id=self.data_source.id,
            destination_config={"type": "data_warehouse"},
            transformation_rules={},
            schedule="0 0 * * *",
            status=PipelineStatus.INACTIVE,
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(pipeline)
        self.db.commit()

        response = client.post(f"/api/v1/etl/pipelines/{pipeline.id}/resume")
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertEqual(data["status"], "active")

    def test_get_pipeline_runs(self):
        """Test getting pipeline run history"""
        # Create pipeline
        pipeline = ETLPipeline(
            organization_id=self.org.id,
            name="Pipeline with Runs",
            source_id=self.data_source.id,
            destination_config={"type": "data_warehouse"},
            transformation_rules={},
            schedule="0 0 * * *",
            status=PipelineStatus.ACTIVE,
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(pipeline)
        self.db.commit()

        response = client.get(f"/api/v1/etl/pipelines/{pipeline.id}/runs")
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertIsInstance(data, list)

    def test_validate_transformation_rules(self):
        """Test validation of transformation rules"""
        pipeline_data = {
            "name": "Pipeline with Complex Rules",
            "organization_id": self.org.id,
            "source_id": self.data_source.id,
            "destination_config": {
                "type": "data_warehouse"
            },
            "transformation_rules": {
                "field_mapping": {
                    "source_field": "destination_field"
                },
                "filters": [
                    {"field": "amount", "operator": "greater_than", "value": 1000}
                ],
                "aggregations": [
                    {"field": "revenue", "function": "sum", "group_by": "region"}
                ]
            },
            "schedule": "0 0 * * *"
        }

        response = client.post("/api/v1/etl/pipelines/validate", json=pipeline_data)
        self.assertEqual(response.status_code, 200)

        data = response.json()
        self.assertTrue(data["is_valid"])


class TestTransformationEngine(unittest.TestCase):
    """Test ETL transformation functionality"""

    def test_field_mapping_transformation(self):
        """Test basic field mapping"""
        transformation = {
            "type": "field_mapping",
            "config": {
                "mappings": {
                    "first_name": "given_name",
                    "last_name": "family_name"
                }
            }
        }

        input_data = {
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>"
        }

        response = client.post(
            "/api/v1/etl/transform",
            json={"transformation": transformation, "data": input_data}
        )
        self.assertEqual(response.status_code, 200)

        result = response.json()
        self.assertEqual(result["given_name"], "John")
        self.assertEqual(result["family_name"], "Doe")
        self.assertEqual(result["email"], "<EMAIL>")

    def test_filter_transformation(self):
        """Test filtering data"""
        transformation = {
            "type": "filter",
            "config": {
                "conditions": [
                    {"field": "age", "operator": ">=", "value": 18}
                ]
            }
        }

        input_data = [
            {"name": "Alice", "age": 25},
            {"name": "Bob", "age": 17},
            {"name": "Charlie", "age": 30}
        ]

        response = client.post(
            "/api/v1/etl/transform/batch",
            json={"transformation": transformation, "data": input_data}
        )
        self.assertEqual(response.status_code, 200)

        result = response.json()
        self.assertEqual(len(result), 2)
        self.assertTrue(all(item["age"] >= 18 for item in result))

    def test_aggregation_transformation(self):
        """Test data aggregation"""
        transformation = {
            "type": "aggregation",
            "config": {
                "group_by": ["department"],
                "aggregations": [
                    {"field": "salary", "function": "avg", "alias": "avg_salary"},
                    {"field": "id", "function": "count", "alias": "employee_count"}
                ]
            }
        }

        input_data = [
            {"id": 1, "department": "Engineering", "salary": 100000},
            {"id": 2, "department": "Engineering", "salary": 120000},
            {"id": 3, "department": "Sales", "salary": 80000},
            {"id": 4, "department": "Sales", "salary": 90000}
        ]

        response = client.post(
            "/api/v1/etl/transform/batch",
            json={"transformation": transformation, "data": input_data}
        )
        self.assertEqual(response.status_code, 200)

        result = response.json()
        self.assertEqual(len(result), 2)

        eng_dept = next(r for r in result if r["department"] == "Engineering")
        self.assertEqual(eng_dept["avg_salary"], 110000)
        self.assertEqual(eng_dept["employee_count"], 2)


if __name__ == "__main__":
    unittest.main()