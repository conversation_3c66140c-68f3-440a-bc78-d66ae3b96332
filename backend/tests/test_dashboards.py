"""
Test suite for Dashboard Builder API endpoints
Testing dashboard CRUD operations, widget management, and templates
"""
import pytest
from datetime import datetime, UTC
import json
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from unittest.mock import Mock, MagicMock
from main import app
from models.models import Dashboard, Widget, DashboardVersion, User, Organization, OrganizationUser
from models.database import get_db
from auth.dependencies import get_current_user
from schemas.schemas import DashboardCreate, WidgetCreate, DashboardUpdate, WidgetUpdate


@pytest.fixture
def sample_user():
    user = User(
        id=1,
        username="testuser",
        email="<EMAIL>",
        hashed_password="$2b$12$KIXxPfR.K.YlqH",  # dummy hash
        is_active=True,
        is_verified=True,
        role="user"  # Add role for testing
    )
    # Add organization_id attribute for authorization checks
    user.organization_id = 1
    return user


@pytest.fixture
def sample_db_session(sample_user, sample_dashboard, sample_widget):
    """Mock database session that returns our test data"""

    # Create a mock database session
    mock_db = Mock()

    # Set up query method to return proper mock queries
    def mock_query(model):
        mock_query_obj = Mock()

        # Set up filter method
        mock_filter = Mock()
        mock_query_obj.filter.return_value = mock_filter
        mock_query_obj.filter_by.return_value = mock_filter

        # Set up the chained methods - default behavior for existing resources
        if model == Dashboard:
            mock_filter.first.return_value = sample_dashboard
            mock_filter.all.return_value = [sample_dashboard]
            mock_filter.count.return_value = 1
            mock_filter.offset.return_value = mock_filter
            mock_filter.limit.return_value = mock_filter
            mock_filter.order_by.return_value = mock_filter
        elif model == Widget:
            mock_filter.first.return_value = sample_widget
            mock_filter.all.return_value = [sample_widget]
            mock_filter.count.return_value = 1
            mock_filter.offset.return_value = mock_filter
            mock_filter.limit.return_value = mock_filter
            mock_filter.order_by.return_value = mock_filter
        elif model == User:
            mock_filter.first.return_value = sample_user
            mock_filter.all.return_value = [sample_user]
            mock_filter.count.return_value = 1
            mock_filter.offset.return_value = mock_filter
            mock_filter.limit.return_value = mock_filter
            mock_filter.order_by.return_value = mock_filter
        else:
            mock_filter.first.return_value = None
            mock_filter.all.return_value = []
            mock_filter.count.return_value = 0
            mock_filter.offset.return_value = mock_filter
            mock_filter.limit.return_value = mock_filter
            mock_filter.order_by.return_value = mock_filter

        return mock_query_obj

    mock_db.query.side_effect = mock_query

    # Set up other database methods
    def mock_add(obj):
        if not hasattr(obj, 'id') or obj.id is None:
            obj.id = 1
        return obj

    mock_db.add.side_effect = mock_add
    mock_db.commit.return_value = None
    mock_db.refresh.return_value = None
    mock_db.delete.return_value = None

    return mock_db


@pytest.fixture
def client(sample_user, sample_dashboard, sample_widget, sample_db_session):
    """Test client with dependency overrides"""
    app.dependency_overrides[get_db] = lambda: sample_db_session
    app.dependency_overrides[get_current_user] = lambda: sample_user

    client = TestClient(app)
    yield client

    # Clean up overrides after test
    app.dependency_overrides.clear()


@pytest.fixture
def sample_dashboard():
    return Dashboard(
        id=1,
        organization_id=1,
        owner_id=1,
        name="Sales Dashboard",
        description="Q4 Sales Performance",
        is_public=False,
        is_template=False,
        configuration={
            "rows": 3,
            "columns": 4,
            "widgets": []
        },
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC)
    )


@pytest.fixture
def sample_widget():
    return Widget(
        id=1,
        dashboard_id=1,
        title="Revenue Chart",
        widget_type="line_chart",
        configuration={
            "dataSource": "sales_data",
            "metrics": ["revenue", "profit"],
            "dimensions": ["date", "region"]
        },
        position={"x": 0, "y": 0, "width": 2, "height": 1},
        created_at=datetime.now(UTC)
    )


class TestDashboardCRUD:
    """Test Dashboard CRUD operations"""

    def test_create_dashboard_success(self, client):
        """Test successful dashboard creation"""
        dashboard_data = {
            "name": "Marketing Dashboard",
            "description": "Campaign performance tracking",
            "organization_id": 1,
            "configuration": {
        "rows": 4,
        "columns": 6,
        "widgets": []
            },
            "is_template": False
        }

        response = client.post("/api/v1/dashboards", json=dashboard_data)

        if response.status_code != 201:
            print(f"Error response: {response.status_code}")
            print(f"Error content: {response.text}")

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == dashboard_data["name"]
        assert data["description"] == dashboard_data["description"]
        assert "id" in data
        assert "created_at" in data

    def test_create_dashboard_invalid_layout(self, client, sample_db_session, sample_user):
        """Test dashboard creation with invalid layout configuration"""
        dashboard_data = {
            "name": "Invalid Dashboard",
            "description": "Testing invalid layout",
            "organization_id": 1,
            "configuration": {
        "rows": -1,  # Invalid: negative rows
        "columns": 0,  # Invalid: zero columns
        "widgets": []
            }
        }

        response = client.post("/api/v1/dashboards", json=dashboard_data)

        assert response.status_code == 422
        assert "validation error" in response.json()["detail"].lower()

    def test_get_dashboard_by_id(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test retrieving a dashboard by ID"""
        response = client.get(f"/api/v1/dashboards/{sample_dashboard.id}")

        assert response.status_code == 200
        data = response.json()
        assert data["id"] == sample_dashboard.id
        assert data["name"] == sample_dashboard.name

    def test_get_dashboard_not_found(self, client, sample_user):
        """Test retrieving non-existent dashboard"""
        # Create a mock database session that returns None for Dashboard queries
        mock_db = Mock()

        def mock_query_not_found(model):
            mock_query_obj = Mock()
            mock_filter = Mock()
            mock_query_obj.filter.return_value = mock_filter
            mock_query_obj.filter_by.return_value = mock_filter

            # Always return None for this test (not found scenario)
            mock_filter.first.return_value = None
            mock_filter.all.return_value = []
            mock_filter.count.return_value = 0
            mock_filter.offset.return_value = mock_filter
            mock_filter.limit.return_value = mock_filter
            mock_filter.order_by.return_value = mock_filter

            return mock_query_obj

        mock_db.query.side_effect = mock_query_not_found
        mock_db.add.return_value = None
        mock_db.commit.return_value = None
        mock_db.refresh.return_value = None
        mock_db.delete.return_value = None

        # Override the database dependency for this test
        from main import app
        from models.database import get_db
        app.dependency_overrides[get_db] = lambda: mock_db

        # Create test client
        from fastapi.testclient import TestClient
        client = TestClient(app)

        try:
            response = client.get("/api/v1/dashboards/999")
            assert response.status_code == 404
            assert "not found" in response.json()["detail"].lower()
        finally:
            # Clean up the override
            if get_db in app.dependency_overrides:
                del app.dependency_overrides[get_db]

    def test_update_dashboard(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test updating an existing dashboard"""
        update_data = {
            "name": "Updated Sales Dashboard",
            "description": "Updated description",
            "configuration": {
        "rows": 4,
        "columns": 5,
        "widgets": []
            }
        }

        response = client.put(f"/api/v1/dashboards/{sample_dashboard.id}", json=update_data)

        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["description"] == update_data["description"]

    def test_delete_dashboard(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test deleting a dashboard"""
        response = client.delete(f"/api/v1/dashboards/{sample_dashboard.id}")

        assert response.status_code == 200
        assert response.json()["message"] == "Dashboard deleted successfully"

    def test_list_dashboards(self, client, sample_db_session, sample_user):
        """Test listing dashboards for an organization"""
        response = client.get("/api/v1/dashboards?page=1&limit=10")

        assert response.status_code == 200
        data = response.json()
        assert data["total"] == 1
        assert len(data["items"]) == 1
        assert data["page"] == 1
        assert data["limit"] == 10


class TestWidgetManagement:
    """Test Widget management within dashboards"""

    def test_add_widget_to_dashboard(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test adding a widget to a dashboard"""
        widget_data = {
            "title": "Sales Chart",
            "widget_type": "bar_chart",
            "configuration": {
        "dataSource": "sales_table",
        "metric": "revenue",
        "groupBy": "product"
            },
            "position": {"x": 0, "y": 0, "width": 2, "height": 2}
        }

        response = client.post(
            f"/api/v1/dashboards/{sample_dashboard.id}/widgets",
            json=widget_data
        )

        assert response.status_code == 201
        data = response.json()
        assert data["title"] == widget_data["title"]
        assert data["widget_type"] == widget_data["widget_type"]
        assert "id" in data

    def test_add_widget_invalid_position(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test adding widget with invalid position"""
        widget_data = {
            "title": "Invalid Widget",
            "widget_type": "line_chart",
            "configuration": {},
            "position": {"x": -1, "y": -1, "width": 0, "height": 0}  # Invalid position
        }

        response = client.post(
            f"/api/v1/dashboards/{sample_dashboard.id}/widgets",
            json=widget_data
        )

        assert response.status_code == 422

    def test_update_widget(self, client, sample_db_session, sample_widget, sample_user):
        """Test updating a widget"""
        update_data = {
            "title": "Updated Revenue Chart",
            "configuration": {
        "dataSource": "updated_sales_data",
        "metric": "profit"
            },
            "position": {"x": 1, "y": 1, "width": 3, "height": 2}
        }

        response = client.put(
            f"/api/v1/dashboards/widgets/{sample_widget.id}",
            json=update_data
        )

        assert response.status_code == 200
        data = response.json()
        assert data["title"] == update_data["title"]

    def test_delete_widget(self, client, sample_db_session, sample_widget, sample_user):
        """Test deleting a widget from dashboard"""
        response = client.delete(f"/api/v1/dashboards/widgets/{sample_widget.id}")

        assert response.status_code == 200
        assert response.json()["message"] == "Widget deleted successfully"

    def test_reorder_widgets(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test reordering widgets in a dashboard"""
        reorder_data = {
            "widgets": [
        {"id": 1, "position": {"x": 2, "y": 2, "width": 2, "height": 2}},
        {"id": 2, "position": {"x": 0, "y": 0, "width": 2, "height": 2}},
        {"id": 3, "position": {"x": 2, "y": 0, "width": 2, "height": 2}}
            ]
        }

        response = client.put(
            f"/api/v1/dashboards/{sample_dashboard.id}/widgets/reorder",
            json=reorder_data
        )

        assert response.status_code == 200
        assert response.json()["message"] == "Widgets reordered successfully"


class TestDashboardTemplates:
    """Test Dashboard template functionality"""

    def test_create_template(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test creating a dashboard template"""
        response = client.post(
            f"/api/v1/dashboards/{sample_dashboard.id}/template"
        )

        assert response.status_code == 201
        data = response.json()
        assert data["is_template"] is True
        assert data["name"].endswith("Template")

    def test_list_templates(self, client, sample_db_session, sample_user):
        """Test listing available dashboard templates"""
        response = client.get("/api/v1/dashboards/templates")

        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 0  # At least 0 templates (could be empty)

    def test_create_dashboard_from_template(self, client, sample_db_session, sample_user):
        """Test creating a new dashboard from a template"""
        create_data = {
            "template_id": 1,  # Use sample_dashboard id
            "name": "Q1 Sales Dashboard",
            "description": "Dashboard for Q1 2024"
        }

        response = client.post(
            "/api/v1/dashboards/from-template",
            json=create_data
        )

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == create_data["name"]
        assert data["is_template"] is False


class TestDashboardVersioning:
    """Test Dashboard version control functionality"""

    def test_save_dashboard_version(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test saving a version of dashboard"""
        version_data = {
            "version_name": "v1.0",
            "description": "Initial version"
        }

        response = client.post(
            f"/api/v1/dashboards/{sample_dashboard.id}/versions",
            json=version_data
        )

        assert response.status_code == 201
        data = response.json()
        assert data["version_name"] == version_data["version_name"]
        assert "created_at" in data

    def test_list_dashboard_versions(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test listing versions of a dashboard"""
        response = client.get(f"/api/v1/dashboards/{sample_dashboard.id}/versions")

        assert response.status_code == 200
        data = response.json()
        assert len(data) >= 0  # At least 0 versions

    def test_restore_dashboard_version(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test restoring a previous version of dashboard"""
        response = client.post(
            f"/api/v1/dashboards/{sample_dashboard.id}/versions/1/restore"
        )

        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Dashboard restored successfully"


class TestDashboardSharing:
    """Test Dashboard sharing and permissions"""

    def test_share_dashboard(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test sharing dashboard with other users"""
        share_data = {
            "user_ids": [2, 3, 4],
            "permission_level": "view",
            "expires_at": "2024-12-31T23:59:59Z"
        }

        response = client.post(
            f"/api/v1/dashboards/{sample_dashboard.id}/share",
            json=share_data
        )

        assert response.status_code == 200
        data = response.json()
        assert data["shared_with_count"] == 3
        assert data["permission_level"] == "view"

    def test_generate_public_link(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test generating public shareable link"""
        link_data = {
            "expires_in_days": 7,
            "password_protected": True
        }

        response = client.post(
            f"/api/v1/dashboards/{sample_dashboard.id}/public-link",
            json=link_data
        )

        assert response.status_code == 201
        data = response.json()
        assert "public_url" in data
        assert "expires_at" in data
        assert data["password_protected"] is True


class TestDashboardExport:
    """Test Dashboard export functionality"""

    def test_export_dashboard_json(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test exporting dashboard configuration as JSON"""
        response = client.get(f"/api/v1/dashboards/{sample_dashboard.id}/export?format=json")

        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"
        data = response.json()
        assert data["id"] == sample_dashboard.id
        assert "widgets" in data
        assert "configuration" in data

    def test_duplicate_dashboard(self, client, sample_db_session, sample_dashboard, sample_user):
        """Test duplicating an existing dashboard"""
        duplicate_data = {
            "name": "Sales Dashboard Copy",
            "include_widgets": True
        }

        response = client.post(
            f"/api/v1/dashboards/{sample_dashboard.id}/duplicate",
            json=duplicate_data
        )

        assert response.status_code == 201
        data = response.json()
        assert data["name"] == duplicate_data["name"]
        assert data["id"] != sample_dashboard.id


if __name__ == "__main__":
    pytest.main([__file__, "-v"])