"""
Test suite for Data Source OAuth integrations and connections
Following TDD approach - tests written before implementation
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from unittest.mock import Mock, patch, AsyncMock
import json
from datetime import datetime, timedelta, timezone

from main import app
from models.database import Base, get_db
from models.models import User, Organization, DataSource

# Test database setup
SQLALCHEMY_TEST_DATABASE_URL = "sqlite:///./test_datasources.db"
engine = create_engine(SQLALCHEMY_TEST_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base.metadata.create_all(bind=engine)

def override_get_db():
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db
client = TestClient(app)


class TestDataSourceConnections:
    """Test Data Source Connections functionality"""

    def setup_method(self):
        """Setup test database and test data"""
        Base.metadata.create_all(bind=engine)
        self.db = TestingSessionLocal()

        # Create test organization and user
        self.org = Organization(
            id=1,
            name="Test Org",
            created_at=datetime.now(timezone.utc)
        )
        self.user = User(
            id=1,
            email="<EMAIL>",
            username="testuser",
            hashed_password="hashed",
            is_active=True,
            is_verified=True
        )
        self.db.add(self.org)
        self.db.add(self.user)
        self.db.commit()

    def teardown_method(self):
        """Clean up test database"""
        self.db.query(DataSource).delete()
        self.db.query(User).delete()
        self.db.query(Organization).delete()
        self.db.commit()
        self.db.close()

    # Test OAuth Flow for Salesforce
    def test_salesforce_oauth_initiate(self):
        """Test initiating Salesforce OAuth flow"""
        response = client.get(
            "/api/v1/data-sources/oauth/salesforce/authorize",
            params={"organization_id": 1}
        )
        assert response.status_code == 200
        data = response.json()
        assert "authorization_url" in data
        assert "state" in data
        assert "salesforce.com" in data["authorization_url"]

    def test_salesforce_oauth_callback(self):
        """Test Salesforce OAuth callback handling"""
        response = client.post(
            "/api/v1/data-sources/oauth/salesforce/callback",
            json={
                "code": "test_auth_code",
                "state": "test_state",
                "organization_id": 1
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["source_type"] == "salesforce"
        assert data["status"] == "connected"
        assert "connection_id" in data

    # Test OAuth Flow for HubSpot
    def test_hubspot_oauth_initiate(self):
        """Test initiating HubSpot OAuth flow"""
        response = client.get(
            "/api/v1/data-sources/oauth/hubspot/authorize",
            params={"organization_id": 1}
        )
        assert response.status_code == 200
        data = response.json()
        assert "authorization_url" in data
        assert "state" in data
        assert "hubspot.com" in data["authorization_url"]

    def test_hubspot_oauth_callback(self):
        """Test HubSpot OAuth callback handling"""
        response = client.post(
            "/api/v1/data-sources/oauth/hubspot/callback",
            json={
                "code": "test_auth_code",
                "state": "test_state",
                "organization_id": 1
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["source_type"] == "hubspot"
        assert data["status"] == "connected"
        assert "connection_id" in data

    # Test OAuth Flow for Google Analytics
    def test_google_analytics_oauth_initiate(self):
        """Test initiating Google Analytics OAuth flow"""
        response = client.get(
            "/api/v1/data-sources/oauth/google-analytics/authorize",
            params={"organization_id": 1}
        )
        assert response.status_code == 200
        data = response.json()
        assert "authorization_url" in data
        assert "state" in data
        assert "google.com" in data["authorization_url"]

    def test_google_analytics_oauth_callback(self):
        """Test Google Analytics OAuth callback handling"""
        response = client.post(
            "/api/v1/data-sources/oauth/google-analytics/callback",
            json={
                "code": "test_auth_code",
                "state": "test_state",
                "organization_id": 1
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["source_type"] == "google_analytics"
        assert data["status"] == "connected"
        assert "connection_id" in data

    # Test Connection Testing
    def test_connection_test_salesforce(self):
        """Test Salesforce connection testing"""
        # First create a data source
        source = DataSource(
            organization_id=1,
            source_type="salesforce",
            source_name="Test Salesforce",
            connection_string="encrypted_credentials",
            status="connected"
        )
        self.db.add(source)
        self.db.commit()

        response = client.post(
            f"/api/v1/data-sources/{source.id}/test-connection"
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "message" in data
        assert "test_results" in data

    def test_connection_test_hubspot(self):
        """Test HubSpot connection testing"""
        source = DataSource(
            organization_id=1,
            source_type="hubspot",
            source_name="Test HubSpot",
            connection_string="encrypted_credentials",
            status="connected"
        )
        self.db.add(source)
        self.db.commit()

        response = client.post(
            f"/api/v1/data-sources/{source.id}/test-connection"
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "message" in data
        assert "test_results" in data

    def test_connection_test_google_analytics(self):
        """Test Google Analytics connection testing"""
        source = DataSource(
            organization_id=1,
            source_type="google_analytics",
            source_name="Test GA",
            connection_string="encrypted_credentials",
            status="connected"
        )
        self.db.add(source)
        self.db.commit()

        response = client.post(
            f"/api/v1/data-sources/{source.id}/test-connection"
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "success"
        assert "message" in data
        assert "test_results" in data

    # Test Credential Management
    def test_credential_storage_encryption(self):
        """Test that credentials are properly encrypted before storage"""
        response = client.post(
            "/api/v1/data-sources",
            json={
                "organization_id": 1,
                "source_type": "salesforce",
                "source_name": "Production Salesforce",
                "credentials": {
                    "client_id": "test_client_id",
                    "client_secret": "test_client_secret",
                    "refresh_token": "test_refresh_token"
                }
            }
        )
        assert response.status_code == 201
        data = response.json()

        # Verify credentials are not returned in plain text
        assert "credentials" not in data
        assert "client_secret" not in data
        assert data["id"] is not None

        # Verify encrypted storage in database
        source = self.db.query(DataSource).filter(DataSource.id == data["id"]).first()
        assert source is not None
        assert "test_client_secret" not in source.connection_string
        assert source.connection_string.startswith("encrypted:")

    def test_credential_update(self):
        """Test updating data source credentials"""
        # Create initial data source
        source = DataSource(
            organization_id=1,
            source_type="hubspot",
            source_name="Test HubSpot",
            connection_string="encrypted:old_credentials",
            status="connected"
        )
        self.db.add(source)
        self.db.commit()

        response = client.put(
            f"/api/v1/data-sources/{source.id}/credentials",
            json={
                "credentials": {
                    "api_key": "new_api_key",
                    "access_token": "new_access_token"
                }
            }
        )
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Credentials updated successfully"

        # Verify credentials are encrypted
        updated_source = self.db.query(DataSource).filter(DataSource.id == source.id).first()
        assert "new_api_key" not in updated_source.connection_string
        assert updated_source.connection_string.startswith("encrypted:")

    def test_credential_deletion(self):
        """Test secure deletion of credentials"""
        source = DataSource(
            organization_id=1,
            source_type="google_analytics",
            source_name="Test GA",
            connection_string="encrypted:credentials",
            status="connected"
        )
        self.db.add(source)
        self.db.commit()

        response = client.delete(f"/api/v1/data-sources/{source.id}")
        assert response.status_code == 200

        # Verify source is deleted
        deleted_source = self.db.query(DataSource).filter(DataSource.id == source.id).first()
        assert deleted_source is None

    # Test Error Recovery
    def test_oauth_error_handling(self):
        """Test OAuth error handling"""
        response = client.post(
            "/api/v1/data-sources/oauth/salesforce/callback",
            json={
                "error": "access_denied",
                "error_description": "User denied access",
                "state": "test_state"
            }
        )
        assert response.status_code == 400
        data = response.json()
        assert data["detail"] == "OAuth authorization failed: User denied access"

    def test_connection_retry_mechanism(self):
        """Test connection retry mechanism on failure"""
        source = DataSource(
            organization_id=1,
            source_type="salesforce",
            source_name="Test Salesforce",
            connection_string="encrypted:invalid_credentials",
            status="error"
        )
        self.db.add(source)
        self.db.commit()

        response = client.post(
            f"/api/v1/data-sources/{source.id}/retry-connection"
        )
        assert response.status_code == 200
        data = response.json()
        assert "retry_count" in data
        assert "next_retry_at" in data
        assert data["status"] in ["retrying", "connected", "failed"]

    # Test Data Source Listing and Management
    def test_list_data_sources(self):
        """Test listing all data sources for an organization"""
        # Create multiple data sources
        sources = [
            DataSource(
                organization_id=1,
                source_type="salesforce",
                source_name="SF Production",
                connection_string="encrypted:1",
                status="connected"
            ),
            DataSource(
                organization_id=1,
                source_type="hubspot",
                source_name="HS Marketing",
                connection_string="encrypted:2",
                status="connected"
            ),
            DataSource(
                organization_id=1,
                source_type="google_analytics",
                source_name="GA Website",
                connection_string="encrypted:3",
                status="error"
            )
        ]
        for source in sources:
            self.db.add(source)
        self.db.commit()

        response = client.get(
            "/api/v1/data-sources",
            params={"organization_id": 1}
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
        assert data[0]["source_type"] == "salesforce"
        assert data[1]["source_type"] == "hubspot"
        assert data[2]["status"] == "error"

    def test_get_single_data_source(self):
        """Test getting a single data source"""
        source = DataSource(
            organization_id=1,
            source_type="salesforce",
            source_name="Test Source",
            connection_string="encrypted:test",
            status="connected"
        )
        self.db.add(source)
        self.db.commit()

        response = client.get(f"/api/v1/data-sources/{source.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == source.id
        assert data["source_name"] == "Test Source"
        assert data["source_type"] == "salesforce"
        assert "connection_string" not in data  # Should not expose encrypted credentials

    def test_update_data_source_status(self):
        """Test updating data source status"""
        source = DataSource(
            organization_id=1,
            source_type="hubspot",
            source_name="Test HubSpot",
            connection_string="encrypted:test",
            status="connected"
        )
        self.db.add(source)
        self.db.commit()

        response = client.put(
            f"/api/v1/data-sources/{source.id}/status",
            json={"status": "paused"}
        )
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "paused"

        # Verify in database
        updated_source = self.db.query(DataSource).filter(DataSource.id == source.id).first()
        assert updated_source.status == "paused"


class TestOAuthTokenManagement:
    """Test OAuth token refresh and management"""

    def setup_method(self):
        """Setup test database and test data"""
        Base.metadata.create_all(bind=engine)
        self.db = TestingSessionLocal()

        # Create test organization
        self.org = Organization(
            id=1,
            name="Test Org",
            created_at=datetime.now(timezone.utc)
        )
        self.db.add(self.org)
        self.db.commit()

    def teardown_method(self):
        """Clean up test database"""
        self.db.query(DataSource).delete()
        self.db.query(Organization).delete()
        self.db.commit()
        self.db.close()

    @patch('services.oauth_service.refresh_salesforce_token')
    def test_salesforce_token_refresh(self, mock_refresh):
        """Test Salesforce token refresh"""
        mock_refresh.return_value = {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 3600
        }

        source = DataSource(
            organization_id=1,
            source_type="salesforce",
            source_name="Test SF",
            connection_string="encrypted:old_token",
            status="connected"
        )
        self.db.add(source)
        self.db.commit()

        response = client.post(f"/api/v1/data-sources/{source.id}/refresh-token")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Token refreshed successfully"
        assert data["expires_at"] is not None

    @patch('services.oauth_service.refresh_hubspot_token')
    def test_hubspot_token_refresh(self, mock_refresh):
        """Test HubSpot token refresh"""
        mock_refresh.return_value = {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 21600
        }

        source = DataSource(
            organization_id=1,
            source_type="hubspot",
            source_name="Test HS",
            connection_string="encrypted:old_token",
            status="connected"
        )
        self.db.add(source)
        self.db.commit()

        response = client.post(f"/api/v1/data-sources/{source.id}/refresh-token")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Token refreshed successfully"
        assert data["expires_at"] is not None

    @patch('services.oauth_service.refresh_google_token')
    def test_google_analytics_token_refresh(self, mock_refresh):
        """Test Google Analytics token refresh"""
        mock_refresh.return_value = {
            "access_token": "new_access_token",
            "refresh_token": "new_refresh_token",
            "expires_in": 3600
        }

        source = DataSource(
            organization_id=1,
            source_type="google_analytics",
            source_name="Test GA",
            connection_string="encrypted:old_token",
            status="connected"
        )
        self.db.add(source)
        self.db.commit()

        response = client.post(f"/api/v1/data-sources/{source.id}/refresh-token")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Token refreshed successfully"
        assert data["expires_at"] is not None

    def test_automatic_token_refresh_on_expiry(self):
        """Test automatic token refresh when token expires"""
        # Create a data source with expired token
        expired_time = datetime.now(timezone.utc) - timedelta(hours=1)
        source = DataSource(
            organization_id=1,
            source_type="salesforce",
            source_name="Test SF",
            connection_string="encrypted:expired_token",
            status="connected",
            metadata=json.dumps({"token_expires_at": expired_time.isoformat()})
        )
        self.db.add(source)
        self.db.commit()

        # Attempt to use the data source (should trigger refresh)
        response = client.post(f"/api/v1/data-sources/{source.id}/test-connection")
        assert response.status_code == 200

        # Verify token was refreshed
        updated_source = self.db.query(DataSource).filter(DataSource.id == source.id).first()
        metadata = json.loads(updated_source.metadata)
        from datetime import timezone
        assert datetime.fromisoformat(metadata["token_expires_at"]) > datetime.now(timezone.utc)