Metadata-Version: 2.1
Name: avro
Version: 1.11.3
Summary: Avro is a serialization and RPC framework.
Home-page: https://avro.apache.org/
Author: Apache Avro
Author-email: <EMAIL>
License: Apache License 2.0
Keywords: avro,serialization,rpc
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Programming Language :: Python :: 3.6
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Development Status :: 5 - Production/Stable
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: avro/LICENSE
Requires-Dist: typing-extensions; python_version < "3.8"
Provides-Extra: snappy
Requires-Dist: python-snappy; extra == "snappy"
Provides-Extra: zstandard
Requires-Dist: zstandard; extra == "zstandard"

## Apache Avro™

Apache Avro™ is a data serialization system. To learn more, please visit our [website](https://avro.apache.org/).


### Documentation

Apache Avro documentation is maintained on our [wiki](https://cwiki.apache.org/confluence/display/AVRO/Index).


### Contributing

To contribute to Avro, please read [How to Contribute](https://cwiki.apache.org/confluence/display/AVRO/How+To+Contribute) on the wiki.


### License, Credits and Acknowledgements

License, credits and acknowledgements are maintained in the [LICENSE.txt](https://github.com/apache/avro/blob/master/LICENSE.txt) and [NOTICE.txt](https://github.com/apache/avro/blob/master/NOTICE.txt) in the source code repository. Those files are also included with the installed package.
