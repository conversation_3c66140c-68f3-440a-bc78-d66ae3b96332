{"namespace": "org.apache.avro.mapred.tether", "protocol": "InputProtocol", "doc": "Transmit inputs to a map or reduce task sub-process.", "types": [{"name": "TaskType", "type": "enum", "symbols": ["MAP", "REDUCE"]}], "messages": {"configure": {"doc": "Configure the task.  <PERSON><PERSON> before any other message.", "request": [{"name": "taskType", "type": "TaskType", "doc": "Whether this is a map or reduce task."}, {"name": "inSchema", "type": "string", "doc": "The Avro schema for task input data."}, {"name": "outSchema", "type": "string", "doc": "The Avro schema for task output data."}], "response": "null", "one-way": true}, "partitions": {"doc": "Set the number of map output partitions.", "request": [{"name": "partitions", "type": "int", "doc": "The number of map output partitions."}], "response": "null", "one-way": true}, "input": {"doc": "Send a block of input data to a task.", "request": [{"name": "data", "type": "bytes", "doc": "A sequence of instances of the declared schema."}, {"name": "count", "type": "long", "default": 1, "doc": "The number of instances in this block."}], "response": "null", "one-way": true}, "abort": {"doc": "Called to abort the task.", "request": [], "response": "null", "one-way": true}, "complete": {"doc": "Called when a task's input is complete.", "request": [], "response": "null", "one-way": true}}}