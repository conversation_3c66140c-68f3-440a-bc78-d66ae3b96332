[metadata]
name = avro
version = file: avro/VERSION.txt
description = Avro is a serialization and RPC framework.
long_description = file: README.md
long_description_content_type = text/markdown
keywords = 
	avro
	serialization
	rpc
author = Apache Avro
author_email = <EMAIL>
url = https://avro.apache.org/
license_files = avro/LICENSE
license = Apache License 2.0
classifiers = 
	License :: OSI Approved :: Apache Software License
	Programming Language :: Python :: 3.6
	Programming Language :: Python :: 3.7
	Programming Language :: Python :: 3.8
	Programming Language :: Python :: 3.9
	Programming Language :: Python :: 3.10
	Programming Language :: Python :: 3.11
	Development Status :: 5 - Production/Stable

[bdist_wheel]
universal = 1

[options]
packages = 
	avro
	avro.test
	avro.tether
package_dir = 
	avro = avro
	avro.test = avro/test
	avro.tether = avro/tether
include_package_data = true
install_requires = 
	typing-extensions;python_version<"3.8"
zip_safe = true
python_requires = >=3.6

[options.entry_points]
console_scripts = 
	avro = avro.__main__:main

[options.package_data]
avro = 
	HandshakeRequest.avsc
	HandshakeResponse.avsc
	LICENSE
	NOTICE
	README.md
	VERSION.txt
avro.tether = 
	InputProtocol.avpr
	OutputProtocol.avpr

[options.extras_require]
snappy = python-snappy
zstandard = zstandard

[aliases]
dist = sdist --dist-dir ../../dist/py

[egg_info]
tag_build = 
tag_date = 0

