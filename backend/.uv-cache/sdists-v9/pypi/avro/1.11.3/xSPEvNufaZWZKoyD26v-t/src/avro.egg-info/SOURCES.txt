MANIFEST.in
README.md
pyproject.toml
setup.cfg
setup.py
avro/HandshakeRequest.avsc
avro/HandshakeResponse.avsc
avro/LICENSE
avro/NOTICE
avro/VERSION.txt
avro/__init__.py
avro/__main__.py
avro/codecs.py
avro/compatibility.py
avro/constants.py
avro/datafile.py
avro/errors.py
avro/interop.avsc
avro/io.py
avro/ipc.py
avro/name.py
avro/protocol.py
avro/py.typed
avro/schema.py
avro/timezones.py
avro/tool.py
avro/utils.py
avro.egg-info/PKG-INFO
avro.egg-info/SOURCES.txt
avro.egg-info/dependency_links.txt
avro.egg-info/entry_points.txt
avro.egg-info/requires.txt
avro.egg-info/top_level.txt
avro.egg-info/zip-safe
avro/test/__init__.py
avro/test/gen_interop_data.py
avro/test/mock_tether_parent.py
avro/test/sample_http_client.py
avro/test/sample_http_server.py
avro/test/test_bench.py
avro/test/test_compatibility.py
avro/test/test_datafile.py
avro/test/test_datafile_interop.py
avro/test/test_init.py
avro/test/test_io.py
avro/test/test_ipc.py
avro/test/test_name.py
avro/test/test_protocol.py
avro/test/test_schema.py
avro/test/test_script.py
avro/test/test_tether_task.py
avro/test/test_tether_task_runner.py
avro/test/test_tether_word_count.py
avro/test/word_count_task.py
avro/tether/InputProtocol.avpr
avro/tether/OutputProtocol.avpr
avro/tether/__init__.py
avro/tether/tether_task.py
avro/tether/tether_task_runner.py
avro/tether/util.py