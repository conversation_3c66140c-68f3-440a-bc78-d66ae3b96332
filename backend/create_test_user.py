#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create a test user for dashboard access
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy.orm import Session
from models.database import SessionLocal, engine
from models.models import User, UserRole, Organization, OrganizationUser
from auth.security import get_password_hash
import datetime

def create_test_user():
    """Create a test user with full access"""
    db = SessionLocal()

    try:
        # Check if user already exists
        existing_user = db.query(User).filter(
            (User.email == "<EMAIL>") |
            (User.username == "admin")
        ).first()

        if existing_user:
            print("Test user already exists!")
            print("\nLogin credentials:")
            print("-" * 40)
            print("Email: <EMAIL>")
            print("Username: admin")
            print("Password: Admin123!")
            print("-" * 40)
            return

        # Create organization first
        org = Organization(
            name="DataFlow Test Organization",
            created_at=datetime.datetime.utcnow(),
            is_active=True
        )
        db.add(org)
        db.flush()  # Get the org ID

        # Create test user
        user = User(
            email="<EMAIL>",
            username="admin",
            full_name="Admin User",
            password_hash=get_password_hash("Admin123!"),
            role=UserRole.ADMIN,
            is_active=True,
            is_verified=True,
            created_at=datetime.datetime.utcnow(),
            organization_id=org.id
        )
        db.add(user)
        db.flush()

        # Create organization-user relationship
        org_user = OrganizationUser(
            organization_id=org.id,
            user_id=user.id,
            role="admin",
            joined_at=datetime.datetime.utcnow()
        )
        db.add(org_user)

        db.commit()

        print("✅ Test user created successfully!")
        print("\nLogin credentials:")
        print("-" * 40)
        print("Email: <EMAIL>")
        print("Username: admin")
        print("Password: Admin123!")
        print("-" * 40)
        print("\nYou can login with either email or username.")

    except Exception as e:
        db.rollback()
        print(f"Error creating test user: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    create_test_user()