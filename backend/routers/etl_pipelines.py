"""
ETL Pipeline Router for pipeline management and transformation operations
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
import uuid

from models.database import get_db
from models.models import ETLPipeline, PipelineStatus, DataSource, Organization, PipelineRun, RunStatus
from schemas.schemas import (
    ETLPipelineCreate,
    ETLPipelineUpdate,
    ETLPipelineResponse,
    PipelineRunResponse,
    TransformationRequest,
    TransformationResponse,
    PipelineValidationRequest,
    PipelineValidationResponse
)

router = APIRouter(prefix="/api/v1/etl", tags=["ETL Pipelines"])


@router.post("/pipelines", response_model=ETLPipelineResponse, status_code=status.HTTP_201_CREATED)
def create_pipeline(
    pipeline: ETLPipelineCreate,
    db: Session = Depends(get_db)
):
    """Create a new ETL pipeline"""
    # Verify organization exists
    org = db.query(Organization).filter(Organization.id == pipeline.organization_id).first()
    if not org:
        raise HTTPException(status_code=404, detail="Organization not found")

    # Verify data source exists and belongs to organization
    source = db.query(DataSource).filter(
        DataSource.id == pipeline.source_id,
        DataSource.organization_id == pipeline.organization_id
    ).first()
    if not source:
        raise HTTPException(status_code=404, detail="Data source not found")

    # Create pipeline
    db_pipeline = ETLPipeline(
        organization_id=pipeline.organization_id,
        name=pipeline.name,
        source_id=pipeline.source_id,
        destination_config=pipeline.destination_config.model_dump() if hasattr(pipeline.destination_config, 'model_dump') else (pipeline.destination_config.dict() if hasattr(pipeline.destination_config, 'dict') else pipeline.destination_config),
        transformation_rules=pipeline.transformation_rules.model_dump() if hasattr(pipeline.transformation_rules, 'model_dump') else (pipeline.transformation_rules.dict() if hasattr(pipeline.transformation_rules, 'dict') else pipeline.transformation_rules),
        schedule=pipeline.schedule,
        status=PipelineStatus.ACTIVE,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )

    db.add(db_pipeline)
    db.commit()
    db.refresh(db_pipeline)

    return ETLPipelineResponse(
        id=db_pipeline.id,
        name=db_pipeline.name,
        organization_id=db_pipeline.organization_id,
        source_id=db_pipeline.source_id,
        destination_config=db_pipeline.destination_config,
        transformation_rules=db_pipeline.transformation_rules,
        schedule=db_pipeline.schedule,
        status=db_pipeline.status.value if hasattr(db_pipeline.status, 'value') else db_pipeline.status,
        created_at=db_pipeline.created_at,
        updated_at=db_pipeline.updated_at
    )


@router.get("/pipelines", response_model=List[ETLPipelineResponse])
def list_pipelines(
    organization_id: int,
    db: Session = Depends(get_db)
):
    """List all pipelines for an organization"""
    pipelines = db.query(ETLPipeline).filter(
        ETLPipeline.organization_id == organization_id
    ).all()

    return [
        ETLPipelineResponse(
            id=p.id,
            name=p.name,
            organization_id=p.organization_id,
            source_id=p.source_id,
            destination_config=p.destination_config,
            transformation_rules=p.transformation_rules,
            schedule=p.schedule,
            status=p.status.value if hasattr(p.status, 'value') else p.status,
            created_at=p.created_at,
            updated_at=p.updated_at
        ) for p in pipelines
    ]


@router.get("/pipelines/{pipeline_id}", response_model=ETLPipelineResponse)
def get_pipeline(
    pipeline_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific pipeline"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")

    return ETLPipelineResponse(
        id=pipeline.id,
        name=pipeline.name,
        organization_id=pipeline.organization_id,
        source_id=pipeline.source_id,
        destination_config=pipeline.destination_config,
        transformation_rules=pipeline.transformation_rules,
        schedule=pipeline.schedule,
        status=pipeline.status.value if hasattr(pipeline.status, 'value') else pipeline.status,
        created_at=pipeline.created_at,
        updated_at=pipeline.updated_at
    )


@router.put("/pipelines/{pipeline_id}", response_model=ETLPipelineResponse)
def update_pipeline(
    pipeline_id: int,
    pipeline_update: ETLPipelineUpdate,
    db: Session = Depends(get_db)
):
    """Update pipeline configuration"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")

    # Update fields if provided
    if pipeline_update.name is not None:
        pipeline.name = pipeline_update.name
    if pipeline_update.destination_config is not None:
        pipeline.destination_config = pipeline_update.destination_config.dict() if hasattr(pipeline_update.destination_config, 'dict') else pipeline_update.destination_config
    if pipeline_update.transformation_rules is not None:
        pipeline.transformation_rules = pipeline_update.transformation_rules.dict() if hasattr(pipeline_update.transformation_rules, 'dict') else pipeline_update.transformation_rules
    if pipeline_update.schedule is not None:
        pipeline.schedule = pipeline_update.schedule
    if pipeline_update.status is not None:
        try:
            pipeline.status = PipelineStatus[pipeline_update.status.upper()]
        except KeyError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid status: {pipeline_update.status}. Must be one of: {[s.name for s in PipelineStatus]}"
            )

    pipeline.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(pipeline)

    return ETLPipelineResponse(
        id=pipeline.id,
        name=pipeline.name,
        organization_id=pipeline.organization_id,
        source_id=pipeline.source_id,
        destination_config=pipeline.destination_config,
        transformation_rules=pipeline.transformation_rules,
        schedule=pipeline.schedule,
        status=pipeline.status.value if hasattr(pipeline.status, 'value') else pipeline.status,
        created_at=pipeline.created_at,
        updated_at=pipeline.updated_at
    )


@router.delete("/pipelines/{pipeline_id}")
def delete_pipeline(
    pipeline_id: int,
    db: Session = Depends(get_db)
):
    """Delete a pipeline"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")

    db.delete(pipeline)
    db.commit()

    return {"message": "Pipeline deleted successfully"}


@router.post("/pipelines/{pipeline_id}/run", response_model=PipelineRunResponse)
def run_pipeline(
    pipeline_id: int,
    db: Session = Depends(get_db)
):
    """Manually trigger a pipeline run"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")

    # Create a new pipeline run
    run = PipelineRun(
        pipeline_id=pipeline_id,
        status=RunStatus.RUNNING,
        started_at=datetime.now(timezone.utc),
        run_metadata={"trigger": "manual"}
    )

    db.add(run)
    db.commit()
    db.refresh(run)

    # In a real implementation, this would trigger the actual ETL process
    # For now, we'll just return the run information
    return PipelineRunResponse(
        run_id=str(run.id),
        pipeline_id=run.pipeline_id,
        status="running",
        started_at=run.started_at,
        finished_at=run.finished_at,
        run_metadata=run.run_metadata
    )


@router.post("/pipelines/{pipeline_id}/pause", response_model=ETLPipelineResponse)
def pause_pipeline(
    pipeline_id: int,
    db: Session = Depends(get_db)
):
    """Pause an active pipeline"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")

    pipeline.status = PipelineStatus.INACTIVE
    pipeline.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(pipeline)

    return ETLPipelineResponse(
        id=pipeline.id,
        name=pipeline.name,
        organization_id=pipeline.organization_id,
        source_id=pipeline.source_id,
        destination_config=pipeline.destination_config,
        transformation_rules=pipeline.transformation_rules,
        schedule=pipeline.schedule,
        status="inactive",
        created_at=pipeline.created_at,
        updated_at=pipeline.updated_at
    )


@router.post("/pipelines/{pipeline_id}/resume", response_model=ETLPipelineResponse)
def resume_pipeline(
    pipeline_id: int,
    db: Session = Depends(get_db)
):
    """Resume a paused pipeline"""
    pipeline = db.query(ETLPipeline).filter(ETLPipeline.id == pipeline_id).first()
    if not pipeline:
        raise HTTPException(status_code=404, detail="Pipeline not found")

    pipeline.status = PipelineStatus.ACTIVE
    pipeline.updated_at = datetime.now(timezone.utc)

    db.commit()
    db.refresh(pipeline)

    return ETLPipelineResponse(
        id=pipeline.id,
        name=pipeline.name,
        organization_id=pipeline.organization_id,
        source_id=pipeline.source_id,
        destination_config=pipeline.destination_config,
        transformation_rules=pipeline.transformation_rules,
        schedule=pipeline.schedule,
        status="active",
        created_at=pipeline.created_at,
        updated_at=pipeline.updated_at
    )


@router.get("/pipelines/{pipeline_id}/runs", response_model=List[PipelineRunResponse])
def get_pipeline_runs(
    pipeline_id: int,
    limit: int = 10,
    db: Session = Depends(get_db)
):
    """Get pipeline run history"""
    runs = db.query(PipelineRun).filter(
        PipelineRun.pipeline_id == pipeline_id
    ).order_by(PipelineRun.started_at.desc()).limit(limit).all()

    return [
        PipelineRunResponse(
            run_id=str(run.id),
            pipeline_id=run.pipeline_id,
            status=run.status.value if hasattr(run.status, 'value') else run.status,
            started_at=run.started_at,
            finished_at=run.finished_at,
            run_metadata=run.run_metadata
        ) for run in runs
    ]


@router.post("/pipelines/validate", response_model=PipelineValidationResponse)
def validate_pipeline(
    pipeline: PipelineValidationRequest,
    db: Session = Depends(get_db)
):
    """Validate pipeline transformation rules"""
    # Basic validation logic
    is_valid = True
    errors = []
    warnings = []

    # Validate source exists
    if hasattr(pipeline, 'source_id'):
        source = db.query(DataSource).filter(DataSource.id == pipeline.source_id).first()
        if not source:
            is_valid = False
            errors.append("Source data source not found")

    # Validate transformation rules
    if hasattr(pipeline, 'transformation_rules') and pipeline.transformation_rules:
        rules = pipeline.transformation_rules.dict() if hasattr(pipeline.transformation_rules, 'dict') else pipeline.transformation_rules

        # Check field mapping
        if 'field_mapping' in rules and not rules['field_mapping']:
            warnings.append("No field mapping defined")

        # Check filters
        if 'filters' in rules:
            for filter_rule in rules['filters']:
                if 'field' not in filter_rule or 'operator' not in filter_rule or 'value' not in filter_rule:
                    errors.append("Invalid filter rule structure")
                    is_valid = False

        # Check aggregations
        if 'aggregations' in rules:
            for agg in rules['aggregations']:
                if 'field' not in agg or 'function' not in agg:
                    errors.append("Invalid aggregation rule structure")
                    is_valid = False

    return PipelineValidationResponse(
        is_valid=is_valid,
        errors=errors if errors else None,
        warnings=warnings if warnings else None
    )


@router.post("/transform", response_model=TransformationResponse)
def transform_data(
    request: TransformationRequest,
    db: Session = Depends(get_db)
):
    """Apply transformation to single data record"""
    transformation = request.transformation
    data = request.data

    result = data.copy() if isinstance(data, dict) else data

    if transformation['type'] == 'field_mapping':
        # Apply field mapping transformation
        if isinstance(result, dict):
            config = transformation['config']
            mappings = config.get('mappings', {})

            new_result = {}
            for old_key, new_key in mappings.items():
                if old_key in result:
                    new_result[new_key] = result[old_key]

            # Keep unmapped fields
            for key, value in result.items():
                if key not in mappings:
                    new_result[key] = value

            result = new_result

    return TransformationResponse(
        transformed_data=result,
        transformation_applied=transformation
    )


@router.post("/transform/batch", response_model=List[Dict[str, Any]])
def transform_batch(
    request: TransformationRequest,
    db: Session = Depends(get_db)
):
    """Apply transformation to batch of data"""
    transformation = request.transformation
    data = request.data if isinstance(request.data, list) else [request.data]

    results = []

    if transformation['type'] == 'filter':
        # Apply filter transformation
        config = transformation['config']
        conditions = config.get('conditions', [])

        for record in data:
            include = True
            for condition in conditions:
                field = condition.get('field')
                operator = condition.get('operator')
                value = condition.get('value')

                if field and field in record:
                    if operator == '>=' and record[field] < value:
                        include = False
                        break
                    elif operator == '>' and record[field] <= value:
                        include = False
                        break
                    elif operator == '<=' and record[field] > value:
                        include = False
                        break
                    elif operator == '<' and record[field] >= value:
                        include = False
                        break
                    elif operator == '==' and record[field] != value:
                        include = False
                        break

            if include:
                results.append(record)

    elif transformation['type'] == 'aggregation':
        # Apply aggregation transformation
        config = transformation['config']
        group_by = config.get('group_by', [])
        aggregations = config.get('aggregations', [])

        # Group data
        groups = {}
        for record in data:
            if group_by:
                key = tuple(record.get(field) for field in group_by)
                if key not in groups:
                    groups[key] = []
                groups[key].append(record)
            else:
                if 'all' not in groups:
                    groups['all'] = []
                groups['all'].append(record)

        # Apply aggregations
        for key, group_data in groups.items():
            result = {}

            # Add group by fields
            if group_by and key != 'all':
                for i, field in enumerate(group_by):
                    result[field] = key[i]

            # Apply aggregation functions
            for agg in aggregations:
                field = agg.get('field')
                function = agg.get('function')
                alias = agg.get('alias', f"{function}_{field}")

                if function == 'avg':
                    values = [r.get(field, 0) for r in group_data]
                    result[alias] = sum(values) / len(values) if values else 0
                elif function == 'sum':
                    result[alias] = sum(r.get(field, 0) for r in group_data)
                elif function == 'count':
                    result[alias] = len(group_data)
                elif function == 'min':
                    values = [r.get(field) for r in group_data if field in r]
                    result[alias] = min(values) if values else None
                elif function == 'max':
                    values = [r.get(field) for r in group_data if field in r]
                    result[alias] = max(values) if values else None

            results.append(result)

    else:
        # Unknown transformation type, return original data
        results = data

    return results