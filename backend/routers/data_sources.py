"""
Data Source OAuth Integration and Management Router
Following TDD - implementing endpoints to pass tests
"""
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta, timezone
import secrets
import json
import hashlib
from urllib.parse import urlencode

from models.database import get_db
from models.models import DataSource, Organization, User, DataSourceType
from services.oauth_service import OAuthService
from services.encryption_service import EncryptionService

router = APIRouter(
    prefix="/api/v1/data-sources",
    tags=["data_sources"],
    responses={404: {"description": "Not found"}},
)

# Initialize services
oauth_service = OAuthService()
encryption_service = EncryptionService()


# OAuth Authorization Endpoints
@router.get("/oauth/{provider}/authorize")
async def initiate_oauth_flow(
    provider: str,
    organization_id: int = Query(...),
    db: Session = Depends(get_db)
):
    """Initiate OAuth flow for a data source provider"""

    # Validate provider
    valid_providers = [
        "salesforce", "hubspot", "google-analytics",
        "dynamics365", "pipedrive", "mixpanel", "amplitude",
        "mailchimp", "marketo", "postgresql", "mysql", "mongodb",
        "aws-s3", "gcs", "shopify", "stripe", "facebook-ads", "linkedin"
    ]
    if provider not in valid_providers:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid provider. Must be one of: {valid_providers}"
        )

    # Generate state token for CSRF protection
    state = secrets.token_urlsafe(32)

    # Store state in session/cache with organization_id
    # In production, use Redis or similar for state storage

    # Build authorization URL based on provider
    if provider == "salesforce":
        auth_url = build_salesforce_auth_url(state)
    elif provider == "hubspot":
        auth_url = build_hubspot_auth_url(state)
    elif provider == "google-analytics":
        auth_url = build_google_analytics_auth_url(state)
    elif provider == "dynamics365":
        auth_url = build_dynamics365_auth_url(state)
    elif provider == "pipedrive":
        auth_url = build_pipedrive_auth_url(state)
    elif provider == "mixpanel":
        auth_url = build_mixpanel_auth_url(state)
    elif provider == "amplitude":
        auth_url = build_amplitude_auth_url(state)
    elif provider == "mailchimp":
        auth_url = build_mailchimp_auth_url(state)
    elif provider == "marketo":
        auth_url = build_marketo_auth_url(state)
    elif provider == "shopify":
        auth_url = build_shopify_auth_url(state)
    elif provider == "stripe":
        auth_url = build_stripe_auth_url(state)
    elif provider == "facebook-ads":
        auth_url = build_facebook_ads_auth_url(state)
    elif provider == "linkedin":
        auth_url = build_linkedin_auth_url(state)
    elif provider in ["postgresql", "mysql", "mongodb", "aws-s3", "gcs"]:
        # For database and storage connections, return a different flow
        auth_url = f"dataflow://configure/{provider}?state={state}"

    return {
        "authorization_url": auth_url,
        "state": state
    }


@router.post("/oauth/{provider}/callback")
async def handle_oauth_callback(
    provider: str,
    callback_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """Handle OAuth callback from provider"""

    # Check for OAuth errors
    if "error" in callback_data:
        error_description = callback_data.get("error_description", "Unknown error")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"OAuth authorization failed: {error_description}"
        )

    # Validate state to prevent CSRF
    state = callback_data.get("state")
    if not state:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing state parameter"
        )

    # Exchange authorization code for access token
    code = callback_data.get("code")
    organization_id = callback_data.get("organization_id")

    if not code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Missing authorization code"
        )

    # Get access token from provider
    if provider == "salesforce":
        tokens = await exchange_salesforce_code(code)
        source_type = "salesforce"
    elif provider == "hubspot":
        tokens = await exchange_hubspot_code(code)
        source_type = "hubspot"
    elif provider == "google-analytics":
        tokens = await exchange_google_code(code)
        source_type = "google_analytics"
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid provider: {provider}"
        )

    # Encrypt and store credentials
    encrypted_credentials = encryption_service.encrypt(json.dumps(tokens))

    # Map string to enum
    type_map = {
        "salesforce": DataSourceType.SALESFORCE,
        "hubspot": DataSourceType.HUBSPOT,
        "google_analytics": DataSourceType.GOOGLE_ANALYTICS
    }

    # Create or update data source
    data_source = DataSource(
        organization_id=organization_id,
        source_type=type_map.get(source_type),
        name=f"{source_type.title()} Connection",
        connection_config={"encrypted_credentials": encrypted_credentials, "status": "connected"},
        is_active=True,
        created_at=datetime.now(timezone.utc)
    )

    db.add(data_source)
    db.commit()
    db.refresh(data_source)

    return {
        "connection_id": data_source.id,
        "source_type": source_type,
        "status": "connected",
        "message": f"Successfully connected to {source_type.title()}"
    }


# Connection Testing Endpoints
@router.post("/{source_id}/test-connection")
async def test_data_source_connection(
    source_id: int,
    db: Session = Depends(get_db)
):
    """Test connection to a data source"""

    # Get data source
    data_source = db.query(DataSource).filter(DataSource.id == source_id).first()
    if not data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source not found"
        )

    # Test connection based on source type
    try:
        if data_source.source_type == DataSourceType.SALESFORCE:
            test_results = await test_salesforce_connection(data_source)
        elif data_source.source_type == DataSourceType.HUBSPOT:
            test_results = await test_hubspot_connection(data_source)
        elif data_source.source_type == DataSourceType.GOOGLE_ANALYTICS:
            test_results = await test_google_analytics_connection(data_source)
        else:
            test_results = {"status": "unknown", "message": "Unknown source type"}
        return {
            "status": "success",
            "message": f"Successfully connected to {data_source.source_type}",
            "test_results": test_results
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e),
            "test_results": {}
        }


# Credential Management Endpoints
@router.post("")
async def create_data_source(
    data_source_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """Create a new data source with encrypted credentials"""

    organization_id = data_source_data.get("organization_id")
    source_type = data_source_data.get("source_type")
    source_name = data_source_data.get("source_name")
    credentials = data_source_data.get("credentials", {})

    # Encrypt credentials
    encrypted_credentials = encryption_service.encrypt(json.dumps(credentials))

    # Map string to enum if needed
    if isinstance(source_type, str):
        type_map = {
            "salesforce": DataSourceType.SALESFORCE,
            "hubspot": DataSourceType.HUBSPOT,
            "google_analytics": DataSourceType.GOOGLE_ANALYTICS,
            "database": DataSourceType.DATABASE,
            "custom_api": DataSourceType.CUSTOM_API
        }
        source_type = type_map.get(source_type, DataSourceType.CUSTOM_API)

    # Create data source
    data_source = DataSource(
        organization_id=organization_id,
        source_type=source_type,
        name=source_name,
        connection_config={"encrypted_credentials": encrypted_credentials, "status": "connected"},
        is_active=True,
        created_at=datetime.now(timezone.utc)
    )

    db.add(data_source)
    db.commit()
    db.refresh(data_source)

    # Return without exposing credentials
    return {
        "id": data_source.id,
        "organization_id": data_source.organization_id,
        "source_type": data_source.source_type.value if data_source.source_type else None,
        "source_name": data_source.name,
        "status": data_source.connection_config.get("status", "unknown") if data_source.connection_config else "unknown",
        "created_at": data_source.created_at.isoformat() if data_source.created_at else None,
        "updated_at": None
    }


@router.put("/{source_id}/credentials")
async def update_data_source_credentials(
    source_id: int,
    credential_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """Update credentials for a data source"""

    # Get data source
    data_source = db.query(DataSource).filter(DataSource.id == source_id).first()
    if not data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source not found"
        )

    # Encrypt new credentials
    new_credentials = credential_data.get("credentials", {})
    encrypted_credentials = encryption_service.encrypt(json.dumps(new_credentials))

    # Update data source
    data_source.connection_config = {"encrypted_credentials": encrypted_credentials, "status": "connected"}

    db.commit()

    return {
        "message": "Credentials updated successfully",
        "source_id": source_id,
        "updated_at": datetime.now(timezone.utc).isoformat()
    }


@router.delete("/{source_id}")
async def delete_data_source(
    source_id: int,
    db: Session = Depends(get_db)
):
    """Delete a data source and its credentials"""

    # Get data source
    data_source = db.query(DataSource).filter(DataSource.id == source_id).first()
    if not data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source not found"
        )

    # Delete data source
    db.delete(data_source)
    db.commit()

    return {
        "message": "Data source deleted successfully",
        "source_id": source_id
    }


# Error Recovery Endpoints
@router.post("/{source_id}/retry-connection")
async def retry_connection(
    source_id: int,
    db: Session = Depends(get_db)
):
    """Retry connection with retry mechanism"""

    # Get data source
    data_source = db.query(DataSource).filter(DataSource.id == source_id).first()
    if not data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source not found"
        )

    # Implement retry logic
    retry_count = 1
    next_retry_at = datetime.now(timezone.utc) + timedelta(seconds=30)

    # Update status in connection_config
    if not data_source.connection_config:
        data_source.connection_config = {}
    data_source.connection_config["status"] = "retrying"
    data_source.connection_config["retry_count"] = retry_count
    data_source.connection_config["next_retry_at"] = next_retry_at.isoformat()
    db.commit()

    return {
        "source_id": source_id,
        "status": "retrying",
        "retry_count": retry_count,
        "next_retry_at": next_retry_at.isoformat()
    }


# Data Source Management Endpoints
@router.get("")
async def list_data_sources(
    organization_id: int = Query(...),
    db: Session = Depends(get_db)
):
    """List all data sources for an organization"""

    data_sources = db.query(DataSource).filter(
        DataSource.organization_id == organization_id
    ).all()

    return [
        {
            "id": ds.id,
            "source_type": ds.source_type.value if ds.source_type else None,
            "source_name": ds.name,
            "status": ds.connection_config.get("status", "unknown") if ds.connection_config else "unknown",
            "created_at": ds.created_at.isoformat() if ds.created_at else None,
            "updated_at": None
        }
        for ds in data_sources
    ]


@router.get("/{source_id}")
async def get_data_source(
    source_id: int,
    db: Session = Depends(get_db)
):
    """Get a single data source"""

    data_source = db.query(DataSource).filter(DataSource.id == source_id).first()
    if not data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source not found"
        )

    # Return without exposing connection config
    return {
        "id": data_source.id,
        "organization_id": data_source.organization_id,
        "source_type": data_source.source_type.value if data_source.source_type else None,
        "source_name": data_source.name,
        "status": data_source.connection_config.get("status", "unknown") if data_source.connection_config else "unknown",
        "created_at": data_source.created_at.isoformat() if data_source.created_at else None,
        "updated_at": None
    }


@router.put("/{source_id}/status")
async def update_data_source_status(
    source_id: int,
    status_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """Update data source status"""

    data_source = db.query(DataSource).filter(DataSource.id == source_id).first()
    if not data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source not found"
        )

    new_status = status_data.get("status")
    if new_status:
        if not data_source.connection_config:
            data_source.connection_config = {}
        data_source.connection_config["status"] = new_status
        db.commit()

    return {
        "id": data_source.id,
        "status": new_status if new_status else "unknown",
        "updated_at": datetime.now(timezone.utc).isoformat()
    }


# Token Refresh Endpoints
@router.post("/{source_id}/refresh-token")
async def refresh_data_source_token(
    source_id: int,
    db: Session = Depends(get_db)
):
    """Refresh OAuth token for a data source"""

    data_source = db.query(DataSource).filter(DataSource.id == source_id).first()
    if not data_source:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data source not found"
        )

    # Mock token refresh for now
    expires_at = datetime.now(timezone.utc) + timedelta(hours=1)

    return {
        "message": "Token refreshed successfully",
        "expires_at": expires_at.isoformat(),
        "source_id": source_id
    }


# Helper functions for OAuth providers
def build_salesforce_auth_url(state: str) -> str:
    """Build Salesforce OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_SALESFORCE_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/salesforce/callback",
        "state": state
    }
    return f"https://login.salesforce.com/services/oauth2/authorize?{urlencode(params)}"


def build_hubspot_auth_url(state: str) -> str:
    """Build HubSpot OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_HUBSPOT_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/hubspot/callback",
        "state": state,
        "scope": "contacts"
    }
    return f"https://app.hubspot.com/oauth/authorize?{urlencode(params)}"


def build_google_analytics_auth_url(state: str) -> str:
    """Build Google Analytics OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_GOOGLE_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/google-analytics/callback",
        "state": state,
        "scope": "https://www.googleapis.com/auth/analytics.readonly",
        "access_type": "offline"
    }
    return f"https://accounts.google.com/o/oauth2/v2/auth?{urlencode(params)}"


def build_dynamics365_auth_url(state: str) -> str:
    """Build Microsoft Dynamics 365 OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_DYNAMICS_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/dynamics365/callback",
        "state": state,
        "scope": "https://graph.microsoft.com/.default",
    }
    return f"https://login.microsoftonline.com/common/oauth2/v2.0/authorize?{urlencode(params)}"


def build_pipedrive_auth_url(state: str) -> str:
    """Build Pipedrive OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_PIPEDRIVE_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/pipedrive/callback",
        "state": state,
    }
    return f"https://oauth.pipedrive.com/oauth/authorize?{urlencode(params)}"


def build_mixpanel_auth_url(state: str) -> str:
    """Build Mixpanel OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_MIXPANEL_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/mixpanel/callback",
        "state": state,
    }
    return f"https://mixpanel.com/oauth/authorize?{urlencode(params)}"


def build_amplitude_auth_url(state: str) -> str:
    """Build Amplitude OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_AMPLITUDE_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/amplitude/callback",
        "state": state,
    }
    return f"https://auth.amplitude.com/oauth/authorize?{urlencode(params)}"


def build_mailchimp_auth_url(state: str) -> str:
    """Build Mailchimp OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_MAILCHIMP_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/mailchimp/callback",
        "state": state,
    }
    return f"https://login.mailchimp.com/oauth2/authorize?{urlencode(params)}"


def build_marketo_auth_url(state: str) -> str:
    """Build Marketo OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_MARKETO_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/marketo/callback",
        "state": state,
    }
    return f"https://app.marketo.com/oauth/authorize?{urlencode(params)}"


def build_shopify_auth_url(state: str) -> str:
    """Build Shopify OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_SHOPIFY_API_KEY",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/shopify/callback",
        "state": state,
        "scope": "read_products,read_customers,read_orders",
    }
    return f"https://myshopify.com/admin/oauth/authorize?{urlencode(params)}"


def build_stripe_auth_url(state: str) -> str:
    """Build Stripe OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_STRIPE_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/stripe/callback",
        "state": state,
        "scope": "read_only",
    }
    return f"https://connect.stripe.com/oauth/authorize?{urlencode(params)}"


def build_facebook_ads_auth_url(state: str) -> str:
    """Build Facebook Ads OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_FACEBOOK_APP_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/facebook-ads/callback",
        "state": state,
        "scope": "ads_read,ads_management",
    }
    return f"https://www.facebook.com/v18.0/dialog/oauth?{urlencode(params)}"


def build_linkedin_auth_url(state: str) -> str:
    """Build LinkedIn OAuth authorization URL"""
    params = {
        "response_type": "code",
        "client_id": "YOUR_LINKEDIN_CLIENT_ID",
        "redirect_uri": "http://localhost:8000/api/v1/data-sources/oauth/linkedin/callback",
        "state": state,
        "scope": "r_organization_analytics,r_ads_reporting",
    }
    return f"https://www.linkedin.com/oauth/v2/authorization?{urlencode(params)}"


async def exchange_salesforce_code(code: str) -> Dict[str, Any]:
    """Exchange Salesforce authorization code for access token"""
    # Mock implementation - replace with actual API call
    return {
        "access_token": "mock_salesforce_access_token",
        "refresh_token": "mock_salesforce_refresh_token",
        "expires_in": 3600,
        "token_type": "Bearer"
    }


async def exchange_hubspot_code(code: str) -> Dict[str, Any]:
    """Exchange HubSpot authorization code for access token"""
    # Mock implementation - replace with actual API call
    return {
        "access_token": "mock_hubspot_access_token",
        "refresh_token": "mock_hubspot_refresh_token",
        "expires_in": 21600,
        "token_type": "Bearer"
    }


async def exchange_google_code(code: str) -> Dict[str, Any]:
    """Exchange Google authorization code for access token"""
    # Mock implementation - replace with actual API call
    return {
        "access_token": "mock_google_access_token",
        "refresh_token": "mock_google_refresh_token",
        "expires_in": 3600,
        "token_type": "Bearer"
    }


async def test_salesforce_connection(data_source: DataSource) -> Dict[str, Any]:
    """Test Salesforce connection"""
    # Mock implementation - replace with actual API test
    return {
        "connected": True,
        "api_version": "v57.0",
        "organization": "Test Organization"
    }


async def test_hubspot_connection(data_source: DataSource) -> Dict[str, Any]:
    """Test HubSpot connection"""
    # Mock implementation - replace with actual API test
    return {
        "connected": True,
        "portal_id": "12345",
        "account_type": "Marketing Hub Professional"
    }


async def test_google_analytics_connection(data_source: DataSource) -> Dict[str, Any]:
    """Test Google Analytics connection"""
    # Mock implementation - replace with actual API test
    return {
        "connected": True,
        "account_count": 3,
        "property_count": 5
    }