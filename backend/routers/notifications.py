from fastapi import APIRout<PERSON>, Depends, HTTPException, Query, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from typing import List, Optional
from datetime import datetime, timedelta
import uuid
import json

from models.database import get_db
from models.models import Notification, User, NotificationType, NotificationSeverity, NotificationStatus
from schemas.notification_schemas import (
    NotificationCreate,
    NotificationResponse,
    NotificationListResponse,
    NotificationUpdate,
    NotificationMarkAsReadRequest,
    NotificationBulkCreateRequest,
    NotificationFilterRequest,
    NotificationTypeEnum,
    NotificationSeverityEnum,
    NotificationStatusEnum
)

router = APIRouter(
    prefix="/api/v1/notifications",
    tags=["notifications"]
)

# WebSocket connection manager for real-time notifications
class ConnectionManager:
    def __init__(self):
        self.active_connections: dict = {}

    async def connect(self, websocket: WebSocket, user_id: int):
        await websocket.accept()
        if user_id not in self.active_connections:
            self.active_connections[user_id] = []
        self.active_connections[user_id].append(websocket)

    def disconnect(self, websocket: WebSocket, user_id: int):
        if user_id in self.active_connections:
            self.active_connections[user_id].remove(websocket)
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]

    async def send_notification(self, user_id: int, notification: dict):
        if user_id in self.active_connections:
            for connection in self.active_connections[user_id]:
                try:
                    await connection.send_json(notification)
                except:
                    # Connection might be closed, remove it
                    self.disconnect(connection, user_id)

    async def broadcast_to_organization(self, org_id: int, notification: dict, db: Session):
        # Get all users in the organization
        users = db.query(User).filter(User.organizations.any(organization_id=org_id)).all()
        for user in users:
            await self.send_notification(user.id, notification)

manager = ConnectionManager()


# Helper function to get current user (simplified - in production, use proper authentication)
def get_current_user(user_id: int = Query(...), db: Session = Depends(get_db)):
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user


# Create a new notification
@router.post("/", response_model=NotificationResponse)
async def create_notification(
    notification: NotificationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Create notification in database
    db_notification = Notification(
        id=str(uuid.uuid4()),
        user_id=notification.user_id or current_user.id,
        organization_id=notification.organization_id,
        type=NotificationType[notification.type.value.upper()],
        severity=NotificationSeverity[notification.severity.value.upper()],
        status=NotificationStatus.UNREAD,
        title=notification.title,
        message=notification.message,
        details=notification.details,
        action_url=notification.action_url,
        action_label=notification.action_label,
        related_entity_type=notification.related_entity_type,
        related_entity_id=notification.related_entity_id,
        persistent=notification.persistent,
        show_browser=notification.show_browser,
        play_sound=notification.play_sound,
        priority=notification.priority,
        expires_at=notification.expires_at
    )

    db.add(db_notification)
    db.commit()
    db.refresh(db_notification)

    # Send real-time notification via WebSocket
    notification_dict = {
        "id": db_notification.id,
        "type": notification.type.value,
        "severity": notification.severity.value,
        "title": db_notification.title,
        "message": db_notification.message,
        "created_at": db_notification.created_at.isoformat(),
        "show_browser": db_notification.show_browser,
        "play_sound": db_notification.play_sound
    }

    await manager.send_notification(
        db_notification.user_id,
        notification_dict
    )

    return db_notification


# Get notifications for current user
@router.get("/", response_model=NotificationListResponse)
def get_notifications(
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=100),
    status: Optional[NotificationStatusEnum] = None,
    type: Optional[NotificationTypeEnum] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Base query
    query = db.query(Notification).filter(Notification.user_id == current_user.id)

    # Apply filters
    if status:
        query = query.filter(Notification.status == NotificationStatus[status.value.upper()])
    if type:
        query = query.filter(Notification.type == NotificationType[type.value.upper()])

    # Remove expired notifications
    query = query.filter(
        or_(
            Notification.expires_at == None,
            Notification.expires_at > datetime.utcnow()
        )
    )

    # Get total count
    total = query.count()

    # Get unread count
    unread_count = db.query(Notification).filter(
        Notification.user_id == current_user.id,
        Notification.status == NotificationStatus.UNREAD
    ).count()

    # Apply pagination and ordering
    notifications = query.order_by(
        desc(Notification.priority),
        desc(Notification.created_at)
    ).offset((page - 1) * page_size).limit(page_size).all()

    return NotificationListResponse(
        notifications=notifications,
        total=total,
        unread_count=unread_count,
        page=page,
        page_size=page_size
    )


# Get single notification
@router.get("/{notification_id}", response_model=NotificationResponse)
def get_notification(
    notification_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    ).first()

    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")

    return notification


# Mark notification as read
@router.put("/{notification_id}/read", response_model=NotificationResponse)
def mark_as_read(
    notification_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    ).first()

    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")

    notification.status = NotificationStatus.READ
    notification.read_at = datetime.utcnow()

    db.commit()
    db.refresh(notification)

    return notification


# Mark multiple notifications as read
@router.post("/mark-as-read", response_model=dict)
def mark_multiple_as_read(
    request: NotificationMarkAsReadRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    # Update all specified notifications
    updated_count = db.query(Notification).filter(
        Notification.id.in_(request.notification_ids),
        Notification.user_id == current_user.id,
        Notification.status == NotificationStatus.UNREAD
    ).update(
        {
            "status": NotificationStatus.READ,
            "read_at": datetime.utcnow()
        },
        synchronize_session=False
    )

    db.commit()

    return {"updated_count": updated_count}


# Mark all notifications as read
@router.post("/mark-all-as-read", response_model=dict)
def mark_all_as_read(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    updated_count = db.query(Notification).filter(
        Notification.user_id == current_user.id,
        Notification.status == NotificationStatus.UNREAD
    ).update(
        {
            "status": NotificationStatus.READ,
            "read_at": datetime.utcnow()
        },
        synchronize_session=False
    )

    db.commit()

    return {"updated_count": updated_count}


# Archive notification
@router.put("/{notification_id}/archive", response_model=NotificationResponse)
def archive_notification(
    notification_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    ).first()

    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")

    notification.status = NotificationStatus.ARCHIVED
    notification.archived_at = datetime.utcnow()

    db.commit()
    db.refresh(notification)

    return notification


# Delete notification
@router.delete("/{notification_id}", response_model=dict)
def delete_notification(
    notification_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    notification = db.query(Notification).filter(
        Notification.id == notification_id,
        Notification.user_id == current_user.id
    ).first()

    if not notification:
        raise HTTPException(status_code=404, detail="Notification not found")

    db.delete(notification)
    db.commit()

    return {"message": "Notification deleted successfully"}


# Clear all notifications
@router.delete("/clear-all", response_model=dict)
def clear_all_notifications(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    deleted_count = db.query(Notification).filter(
        Notification.user_id == current_user.id
    ).delete(synchronize_session=False)

    db.commit()

    return {"deleted_count": deleted_count}


# Bulk create notifications (admin only - simplified for demo)
@router.post("/bulk", response_model=dict)
async def bulk_create_notifications(
    request: NotificationBulkCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    created_notifications = []

    # Determine target users
    target_users = []
    if request.target_users:
        target_users = db.query(User).filter(User.id.in_(request.target_users)).all()
    elif request.target_organization_id:
        # Get all users in the organization
        from models.models import OrganizationUser
        org_users = db.query(OrganizationUser).filter(
            OrganizationUser.organization_id == request.target_organization_id
        ).all()
        target_users = [ou.user for ou in org_users]
    else:
        target_users = [current_user]

    # Create notifications for each user
    for user in target_users:
        for notification_data in request.notifications:
            db_notification = Notification(
                id=str(uuid.uuid4()),
                user_id=user.id,
                organization_id=request.target_organization_id,
                type=NotificationType[notification_data.type.value.upper()],
                severity=NotificationSeverity[notification_data.severity.value.upper()],
                status=NotificationStatus.UNREAD,
                title=notification_data.title,
                message=notification_data.message,
                details=notification_data.details,
                action_url=notification_data.action_url,
                action_label=notification_data.action_label,
                related_entity_type=notification_data.related_entity_type,
                related_entity_id=notification_data.related_entity_id,
                persistent=notification_data.persistent,
                show_browser=notification_data.show_browser,
                play_sound=notification_data.play_sound,
                priority=notification_data.priority,
                expires_at=notification_data.expires_at
            )

            db.add(db_notification)
            created_notifications.append(db_notification)

            # Send real-time notification
            notification_dict = {
                "id": db_notification.id,
                "type": notification_data.type.value,
                "severity": notification_data.severity.value,
                "title": db_notification.title,
                "message": db_notification.message,
                "created_at": datetime.utcnow().isoformat(),
                "show_browser": db_notification.show_browser,
                "play_sound": db_notification.play_sound
            }

            await manager.send_notification(user.id, notification_dict)

    db.commit()

    return {
        "created_count": len(created_notifications),
        "target_users_count": len(target_users)
    }


# WebSocket endpoint for real-time notifications
@router.websocket("/ws/{user_id}")
async def websocket_endpoint(
    websocket: WebSocket,
    user_id: int,
    db: Session = Depends(get_db)
):
    # Verify user exists
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        await websocket.close(code=4404, reason="User not found")
        return

    await manager.connect(websocket, user_id)

    try:
        # Send initial connection success message
        await websocket.send_json({
            "type": "connection",
            "status": "connected",
            "message": "Connected to notification service"
        })

        # Keep connection alive and handle incoming messages
        while True:
            data = await websocket.receive_text()
            # Handle ping/pong or other messages if needed
            if data == "ping":
                await websocket.send_text("pong")

    except WebSocketDisconnect:
        manager.disconnect(websocket, user_id)
    except Exception as e:
        manager.disconnect(websocket, user_id)
        print(f"WebSocket error for user {user_id}: {e}")