"""
Dashboard Builder API endpoints with comprehensive functionality
Supports CRUD operations, widgets, templates, versioning, sharing, and export
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Body, status
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta, UTC
from models.database import get_db
from models.models import (
    Dashboard, Widget, DashboardVersion,
    User, Organization, OrganizationUser,
    Metric
)
from schemas.schemas import (
    Dashboard as DashboardSchema,
    DashboardCreate,
    DashboardUpdate,
    Widget as WidgetSchema,
    WidgetCreate,
    WidgetCreateRequest,
    WidgetUpdate,
    DashboardStats,
    MetricBase,
    PaginatedDashboardResponse
)
from routers.auth import get_current_user
import json
import secrets
import hashlib

router = APIRouter(prefix="/api/v1/dashboards", tags=["dashboards"])

# Helper function to check user organization access
# Helper function to check user organization access
def check_organization_access(user: User, organization_id: int, db: Session):
    """Verify user has access to the organization"""
    org_user = db.query(OrganizationUser).filter(
        OrganizationUser.user_id == user.id,
        OrganizationUser.organization_id == organization_id
    ).first()

    if not org_user:
        raise HTTPException(status_code=403, detail="Access denied to this organization")
    return True

# Dashboard CRUD Operations
@router.post("/", response_model=DashboardSchema, status_code=status.HTTP_201_CREATED)
async def create_dashboard(
    dashboard: DashboardCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new dashboard"""
    # Validate layout configuration
    if dashboard.configuration:
        if dashboard.configuration.get("rows", 0) <= 0:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Validation error: Rows must be positive"
            )
        if dashboard.configuration.get("columns", 0) <= 0:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Validation error: Columns must be positive"
            )

    # Get user's organization
    org_user = db.query(OrganizationUser).filter(
        OrganizationUser.user_id == current_user.id
    ).first()

    organization_id = org_user.organization_id if org_user else getattr(current_user, 'organization_id', 1)

    db_dashboard = Dashboard(
        organization_id=organization_id,
        name=dashboard.name,
        description=dashboard.description,
        configuration=dashboard.configuration or {"rows": 3, "columns": 4, "widgets": []},
        is_public=dashboard.is_public,
        is_template=dashboard.is_template,
        owner_id=current_user.id
    )

    db.add(db_dashboard)
    db.commit()
    db.refresh(db_dashboard)

    return db_dashboard

@router.get("/{dashboard_id}", response_model=DashboardSchema)
async def get_dashboard(
    dashboard_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a dashboard by ID"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    return dashboard

@router.put("/{dashboard_id}", response_model=DashboardSchema)
async def update_dashboard(
    dashboard_id: int,
    dashboard_update: DashboardUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update an existing dashboard"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    # Update fields
    if dashboard_update.name is not None:
        dashboard.name = dashboard_update.name
    if dashboard_update.description is not None:
        dashboard.description = dashboard_update.description
    if dashboard_update.configuration is not None:
        dashboard.configuration = dashboard_update.configuration

    dashboard.updated_at = datetime.now(UTC)

    db.commit()
    db.refresh(dashboard)

    return dashboard

@router.delete("/{dashboard_id}")
async def delete_dashboard(
    dashboard_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a dashboard"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

@router.delete("/{dashboard_id}")
async def delete_dashboard(
    dashboard_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a dashboard"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    try:
        # Delete associated widgets first
        db.query(Widget).filter(Widget.dashboard_id == dashboard_id).delete()
        
        # Delete dashboard
        db.delete(dashboard)
        db.commit()
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail="Failed to delete dashboard")

    return {"message": "Dashboard deleted successfully"}

    return {"message": "Dashboard deleted successfully"}

@router.get("/", response_model=PaginatedDashboardResponse)
async def list_dashboards(
    page: int = Query(1, ge=1),
    limit: int = Query(10, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List dashboards for an organization"""
    # Get user's organization
    org_user = db.query(OrganizationUser).filter(
        OrganizationUser.user_id == current_user.id
    ).first()

    organization_id = org_user.organization_id if org_user else getattr(current_user, 'organization_id', 1)

    # Get total count
    total = db.query(Dashboard).filter(
        Dashboard.organization_id == organization_id,
        Dashboard.is_template == False
    ).count()

    # Get paginated dashboards
    skip = (page - 1) * limit
    dashboards = db.query(Dashboard).filter(
        Dashboard.organization_id == organization_id,
        Dashboard.is_template == False
    ).offset(skip).limit(limit).all()

    return {
        "total": total,
        "page": page,
        "limit": limit,
        "items": dashboards
    }

# Widget Management
@router.post("/{dashboard_id}/widgets", response_model=WidgetSchema, status_code=status.HTTP_201_CREATED)
async def add_widget(
    dashboard_id: int,
    widget: WidgetCreateRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Add a widget to a dashboard"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    # Validate widget position
    if widget.position:
        if widget.position.get("x", 0) < 0 or widget.position.get("y", 0) < 0:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Widget position cannot be negative"
            )
        if widget.position.get("width", 0) <= 0 or widget.position.get("height", 0) <= 0:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Widget dimensions must be positive"
            )

    db_widget = Widget(
        dashboard_id=dashboard_id,
        title=widget.title,
        widget_type=widget.widget_type,
        configuration=widget.configuration or {},
        position=widget.position,
        created_at=datetime.now(UTC)
    )

    db.add(db_widget)
    db.commit()
    db.refresh(db_widget)

    return db_widget

@router.put("/widgets/{widget_id}", response_model=WidgetSchema)
async def update_widget(
    widget_id: int,
    widget_update: WidgetUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a widget"""
    widget = db.query(Widget).filter(Widget.id == widget_id).first()

    if not widget:
        raise HTTPException(status_code=404, detail="Widget not found")

    # Check dashboard organization access
    dashboard = db.query(Dashboard).filter(Dashboard.id == widget.dashboard_id).first()
    check_organization_access(current_user, dashboard.organization_id, db)

    # Update fields
    if widget_update.title is not None:
        widget.title = widget_update.title
    if widget_update.configuration is not None:
        widget.configuration = widget_update.configuration
    if widget_update.position is not None:
        widget.position = widget_update.position

    db.commit()
    db.refresh(widget)

    return widget

@router.delete("/widgets/{widget_id}")
async def delete_widget(
    widget_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a widget from dashboard"""
    widget = db.query(Widget).filter(Widget.id == widget_id).first()

    if not widget:
        raise HTTPException(status_code=404, detail="Widget not found")

    # Check dashboard organization access
    dashboard = db.query(Dashboard).filter(Dashboard.id == widget.dashboard_id).first()
    check_organization_access(current_user, dashboard.organization_id, db)

    db.delete(widget)
    db.commit()

    return {"message": "Widget deleted successfully"}

@router.put("/{dashboard_id}/widgets/reorder")
async def reorder_widgets(
    dashboard_id: int,
    reorder_data: Dict[str, List[Dict[str, Any]]] = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Reorder widgets in a dashboard"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    # Update widget positions
    for widget_data in reorder_data.get("widgets", []):
        widget = db.query(Widget).filter(
            Widget.id == widget_data["id"],
            Widget.dashboard_id == dashboard_id
        ).first()

        if widget:
            widget.position = widget_data["position"]

    db.commit()

    return {"message": "Widgets reordered successfully"}

# Dashboard Templates
@router.post("/{dashboard_id}/template", response_model=DashboardSchema, status_code=status.HTTP_201_CREATED)
async def create_template(
    dashboard_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a template from existing dashboard"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    # Create template copy
    template = Dashboard(
        organization_id=dashboard.organization_id,
        name=f"{dashboard.name} Template",
        description=f"Template based on {dashboard.name}",
        configuration=dashboard.configuration,
        is_template=True,
        owner_id=current_user.id
    )

    db.add(template)
    db.commit()
    db.refresh(template)

    # Copy widgets to template
    widgets = db.query(Widget).filter(Widget.dashboard_id == dashboard_id).all()
    for widget in widgets:
        template_widget = Widget(
            dashboard_id=template.id,
            title=widget.title,
            widget_type=widget.widget_type,
            configuration=widget.configuration,
            position=widget.position,
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC)
        )
        db.add(template_widget)

    db.commit()

    return template

@router.get("/templates", response_model=List[DashboardSchema])
async def list_templates(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List available dashboard templates"""
    # Get user's organization
    org_user = db.query(OrganizationUser).filter(
        OrganizationUser.user_id == current_user.id
    ).first()

    organization_id = org_user.organization_id if org_user else getattr(current_user, 'organization_id', 1)

    templates = db.query(Dashboard).filter(
        Dashboard.organization_id == organization_id,
        Dashboard.is_template == True
    ).all()

    return templates

@router.post("/from-template", response_model=DashboardSchema, status_code=status.HTTP_201_CREATED)
async def create_from_template(
    data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new dashboard from a template"""
    template = db.query(Dashboard).filter(
        Dashboard.id == data["template_id"],
        Dashboard.is_template == True
    ).first()

    if not template:
        raise HTTPException(status_code=404, detail="Template not found")

    # Check organization access
    check_organization_access(current_user, template.organization_id, db)

    # Create new dashboard from template
    new_dashboard = Dashboard(
        organization_id=template.organization_id,
        name=data["name"],
        description=data.get("description", ""),
        configuration=template.configuration,
        is_template=False,
        owner_id=current_user.id
    )

    db.add(new_dashboard)
    db.commit()
    db.refresh(new_dashboard)

    # Copy widgets from template
    template_widgets = db.query(Widget).filter(Widget.dashboard_id == template.id).all()
    for widget in template_widgets:
        new_widget = Widget(
            dashboard_id=new_dashboard.id,
            title=widget.title,
            widget_type=widget.widget_type,
            configuration=widget.configuration,
            position=widget.position,
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC)
        )
        db.add(new_widget)

    db.commit()

    return new_dashboard

# Dashboard Versioning
@router.post("/{dashboard_id}/versions", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def save_version(
    dashboard_id: int,
    version_data: Dict[str, str] = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Save a version of dashboard"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    # Get all widgets for this dashboard
    widgets = db.query(Widget).filter(Widget.dashboard_id == dashboard_id).all()
    widget_configs = [
        {
            "title": w.title,
            "widget_type": w.widget_type,
            "configuration": w.configuration,
            "position": w.position
        }
        for w in widgets
    ]

    # Create version
    version = DashboardVersion(
        dashboard_id=dashboard_id,
        version_name=version_data["version_name"],
        description=version_data.get("description", ""),
        dashboard_config={
            "name": dashboard.name,
            "description": dashboard.description,
            "configuration": dashboard.configuration,
            "widgets": widget_configs
        },
        owner_id=current_user.id,
        created_at=datetime.now(UTC)
    )

    db.add(version)
    db.commit()
    db.refresh(version)

    return {
        "id": version.id,
        "dashboard_id": version.dashboard_id,
        "version_name": version.version_name,
        "description": version.description,
        "created_at": version.created_at.isoformat()
    }

@router.get("/{dashboard_id}/versions", response_model=List[Dict[str, Any]])
async def list_versions(
    dashboard_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """List versions of a dashboard"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    versions = db.query(DashboardVersion).filter(
        DashboardVersion.dashboard_id == dashboard_id
    ).order_by(DashboardVersion.created_at.desc()).all()

    return [
        {
            "id": v.id,
            "dashboard_id": v.dashboard_id,
            "version_name": v.version_name,
            "description": v.description,
            "created_at": v.created_at.isoformat()
        }
        for v in versions
    ]

@router.post("/{dashboard_id}/versions/{version_id}/restore")
async def restore_version(
    dashboard_id: int,
    version_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Restore a previous version of dashboard"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    version = db.query(DashboardVersion).filter(
        DashboardVersion.id == version_id,
        DashboardVersion.dashboard_id == dashboard_id
    ).first()

    if not version:
        raise HTTPException(status_code=404, detail="Version not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    # Restore dashboard configuration
    config = version.dashboard_config
    dashboard.name = config.get("name", dashboard.name)
    dashboard.description = config.get("description", dashboard.description)
    dashboard.configuration = config.get("configuration", dashboard.configuration)
    dashboard.updated_at = datetime.now(UTC)

    # Delete existing widgets
    db.query(Widget).filter(Widget.dashboard_id == dashboard_id).delete()

    # Restore widgets
    for widget_config in config.get("widgets", []):
        widget = Widget(
            dashboard_id=dashboard_id,
            title=widget_config["title"],
            widget_type=widget_config["widget_type"],
            configuration=widget_config["configuration"],
            position=widget_config["position"],
            created_at=datetime.now(UTC),
            updated_at=datetime.now(UTC)
        )
        db.add(widget)

    db.commit()

    return {
        "message": "Dashboard restored successfully",
        "restored_version": version.version_name
    }

# Dashboard Sharing
@router.post("/{dashboard_id}/share")
async def share_dashboard(
    dashboard_id: int,
    share_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Share dashboard with other users"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    # Here you would implement actual sharing logic
    # For now, return mock response
    return {
        "dashboard_id": dashboard_id,
        "shared_with_count": len(share_data.get("user_ids", [])),
        "permission_level": share_data.get("permission_level", "view"),
        "expires_at": share_data.get("expires_at")
    }

@router.post("/{dashboard_id}/public-link", status_code=status.HTTP_201_CREATED)
async def generate_public_link(
    dashboard_id: int,
    link_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Generate public shareable link"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    # Generate unique public token
    public_token = secrets.token_urlsafe(32)

    # Calculate expiration
    expires_at = datetime.now(UTC) + timedelta(days=link_data.get("expires_in_days", 7))

    # Here you would save this to database
    # For now, return mock response
    return {
        "public_url": f"https://app.dataflow-pro.com/public/dashboard/{public_token}",
        "expires_at": expires_at.isoformat(),
        "password_protected": link_data.get("password_protected", False)
    }

# Dashboard Export
@router.get("/{dashboard_id}/export")
async def export_dashboard(
    dashboard_id: int,
    format: str = Query("json", pattern="^(json|pdf|csv)$"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Export dashboard configuration"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    if format == "json":
        # Get widgets
        widgets = db.query(Widget).filter(Widget.dashboard_id == dashboard_id).all()

        export_data = {
            "id": dashboard.id,
            "name": dashboard.name,
            "description": dashboard.description,
            "configuration": dashboard.configuration,
            "widgets": [
                {
                    "id": w.id,
                    "name": w.name,
                    "widget_type": w.widget_type,
                    "configuration": w.configuration,
                    "position": w.position
                }
                for w in widgets
            ],
            "exported_at": datetime.now(UTC).isoformat()
        }

        return export_data

    # For other formats, you would implement appropriate export logic
    raise HTTPException(status_code=501, detail=f"Export format '{format}' not implemented")

@router.post("/{dashboard_id}/duplicate", response_model=DashboardSchema, status_code=status.HTTP_201_CREATED)
async def duplicate_dashboard(
    dashboard_id: int,
    duplicate_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Duplicate an existing dashboard"""
    dashboard = db.query(Dashboard).filter(Dashboard.id == dashboard_id).first()

    if not dashboard:
        raise HTTPException(status_code=404, detail="Dashboard not found")

    # Check organization access
    check_organization_access(current_user, dashboard.organization_id, db)

    # Create duplicate
    duplicate = Dashboard(
        organization_id=dashboard.organization_id,
        name=duplicate_data["name"],
        description=dashboard.description,
        configuration=dashboard.configuration,
        is_template=False,
        owner_id=current_user.id
    )

    db.add(duplicate)
    db.commit()
    db.refresh(duplicate)

    # Copy widgets if requested
    if duplicate_data.get("include_widgets", False):
        widgets = db.query(Widget).filter(Widget.dashboard_id == dashboard_id).all()
        for widget in widgets:
            new_widget = Widget(
                dashboard_id=duplicate.id,
                title=widget.title,
                widget_type=widget.widget_type,
                configuration=widget.configuration,
                position=widget.position,
                created_at=datetime.now(UTC),
                updated_at=datetime.now(UTC)
            )
            db.add(new_widget)

        db.commit()

    return duplicate

# Dashboard Statistics (from original dashboard.py)
@router.get("/stats", response_model=DashboardStats)
async def get_dashboard_stats(
    organization_id: int = Query(...),
    db: Session = Depends(get_db)
):
    """Get dashboard statistics for the organization"""
    # Simulated statistics - would be calculated from real data
    stats = DashboardStats(
        monthly_active_users=1847,
        connected_sources=247,
        records_processed=3200000,
        data_quality_score=96.0,
        user_growth=12.0,
        source_growth=5,
        processing_growth=18.0,
        quality_improvement=2.0
    )
    return stats