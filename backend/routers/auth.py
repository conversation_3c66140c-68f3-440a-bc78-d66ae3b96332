
"""
Authentication router
Handles user registration, login, and authentication-related endpoints
"""

from datetime import timed<PERSON><PERSON>
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from models.database import get_db
from models.models import User
from schemas.auth import (
    UserCreate, UserResponse, UserLogin, Token,
    PasswordReset, PasswordResetConfirm, EmailVerification,
    ChangePassword, UserUpdate
)
from auth.security import (
    get_password_hash, authenticate_user, create_access_token,
    create_email_verification_token, verify_email_token,
    create_password_reset_token, verify_password_reset_token,
    verify_password, ACCESS_TOKEN_EXPIRE_MINUTES
)
from auth.dependencies import get_current_user, get_current_active_user
from services.email_service import email_service
import logging

logger = logging.getLogger(__name__)


router = APIRouter(prefix="/api/v1/auth", tags=["authentication"])


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user: UserCreate,
    db: Session = Depends(get_db)
):
    """
    Register a new user

    Args:
        user: User creation data
        db: Database session

    Returns:
        Created user data

    Raises:
        HTTPException: If email or username already exists
    """
    # Check if email already exists
    db_user = db.query(User).filter(User.email == user.email).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Check if username already exists
    db_user = db.query(User).filter(User.username == user.username).first()
    if db_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already taken"
        )

    # Create new user
    hashed_password = get_password_hash(user.password)
    db_user = User(
        email=user.email,
        username=user.username,
        full_name=user.full_name,
        hashed_password=hashed_password,
        is_active=True,
        is_verified=False  # Will be verified via email
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Send verification email
    try:
        verification_token = create_email_verification_token(db_user.email)
        email_sent = await email_service.send_verification_email(
            user_email=db_user.email,
            user_name=db_user.full_name or db_user.username,
            verification_token=verification_token
        )

        if not email_sent:
            logger.warning(f"Failed to send verification email to {db_user.email}")
            # Don't fail registration if email fails, just log it

    except Exception as e:
        logger.error(f"Error sending verification email to {db_user.email}: {e}")
        # Don't fail registration if email fails

    return db_user


@router.post("/login", response_model=Token)
async def login_user(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """
    Login user and return access token

    Args:
        form_data: Login form data (username and password)
        db: Database session

    Returns:
        Access token and token type

    Raises:
        HTTPException: If credentials are incorrect
    """
    user = authenticate_user(db, form_data.username, form_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Type narrowing: at this point user is guaranteed to be a User object, not False
    assert isinstance(user, User), "User should be a User object after authentication"
    
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.email}, expires_delta=access_token_expires
    )

    return {
        "access_token": access_token,
        "token_type": "bearer",
        "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60  # Return in seconds
    }


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get current user information

    Args:
        current_user: Current authenticated user

    Returns:
        Current user data
    """
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user profile

    Args:
        user_update: User update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        Updated user data
    """
    # Update only provided fields
    if user_update.full_name is not None:
        current_user.full_name = user_update.full_name

    if user_update.avatar_url is not None:
        current_user.avatar_url = user_update.avatar_url

    db.commit()
    db.refresh(current_user)

    return current_user


@router.post("/change-password")
async def change_password(
    password_data: ChangePassword,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Change user password

    Args:
        password_data: Current and new password
        current_user: Current authenticated user
        db: Database session

    Returns:
        Success message

    Raises:
        HTTPException: If current password is incorrect
    """
    # Verify current password
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect current password"
        )

    # Update password
    current_user.hashed_password = get_password_hash(password_data.new_password)
    db.commit()

    return {"message": "Password updated successfully"}


@router.post("/request-password-reset")
async def request_password_reset(
    password_reset: PasswordReset,
    db: Session = Depends(get_db)
):
    """
    Request password reset

    Args:
        password_reset: Email for password reset
        db: Database session

    Returns:
        Success message (always returns success for security)
    """
    user = db.query(User).filter(User.email == password_reset.email).first()

    if user:
        # Create password reset token
        reset_token = create_password_reset_token(user.email)

        # Send password reset email
        try:
            email_sent = await email_service.send_password_reset_email(
                user_email=user.email,
                user_name=user.full_name or user.username,
                reset_token=reset_token
            )

            if not email_sent:
                logger.warning(f"Failed to send password reset email to {user.email}")

        except Exception as e:
            logger.error(f"Error sending password reset email to {user.email}: {e}")

    # Always return success message for security
    return {"message": "If the email exists, a password reset link has been sent"}


@router.post("/reset-password")
async def reset_password(
    reset_data: PasswordResetConfirm,
    db: Session = Depends(get_db)
):
    """
    Reset password with token

    Args:
        reset_data: Reset token and new password
        db: Database session

    Returns:
        Success message

    Raises:
        HTTPException: If token is invalid or expired
    """
    email = verify_password_reset_token(reset_data.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset token"
        )

    user = db.query(User).filter(User.email == email).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Update password
    user.hashed_password = get_password_hash(reset_data.new_password)
    db.commit()

    return {"message": "Password reset successfully"}


@router.post("/verify-email")
async def verify_email(
    verification: EmailVerification,
    db: Session = Depends(get_db)
):
    """
    Verify email address

    Args:
        verification: Email verification token
        db: Database session

    Returns:
        Success message

    Raises:
        HTTPException: If token is invalid or expired
    """
    email = verify_email_token(verification.token)
    if not email:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification token"
        )

    user = db.query(User).filter(User.email == email).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Mark user as verified
    user.is_verified = True
    db.commit()

    # Send welcome email
    try:
        await email_service.send_welcome_email(
            user_email=user.email,
            user_name=user.full_name or user.username
        )
    except Exception as e:
        logger.error(f"Error sending welcome email to {user.email}: {e}")
        # Don't fail verification if welcome email fails

    return {"message": "Email verified successfully"}


@router.post("/resend-verification")
async def resend_verification_email(
    current_user: User = Depends(get_current_active_user)
):
    """
    Resend email verification

    Args:
        current_user: Current authenticated user

    Returns:
        Success message

    Raises:
        HTTPException: If user is already verified
    """
    if current_user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already verified"
        )

    # Create verification token
    verification_token = create_email_verification_token(current_user.email)

    # Send verification email
    try:
        email_sent = await email_service.send_verification_email(
            user_email=current_user.email,
            user_name=current_user.full_name or current_user.username,
            verification_token=verification_token
        )

        if not email_sent:
            logger.warning(f"Failed to resend verification email to {current_user.email}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to send verification email"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error resending verification email to {current_user.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to send verification email"
        )

    return {"message": "Verification email sent"}


@router.post("/logout")
async def logout_user():
    """
    Logout user (client-side token invalidation)

    Returns:
        Success message

    Note:
        Since we're using stateless JWT tokens, actual logout
        is handled on the client side by removing the token.
        This endpoint is provided for consistency and doesn't
        require authentication.
    """
    return {"message": "Successfully logged out"}
