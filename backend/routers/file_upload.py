"""
File Upload Router
Handles universal file uploads and processing for data analysis
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, BackgroundTasks
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
import logging

from models.database import get_db
from models import models
from schemas import schemas
from services.file_processor_simple import UniversalFileProcessor

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/v1/files",
    tags=["files"],
    responses={404: {"description": "Not found"}},
)

# Initialize file processor
file_processor = UniversalFileProcessor()


@router.post("/upload", response_model=Dict[str, Any])
async def upload_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    organization_id: int = 1,  # TODO: Get from auth context
    user_id: int = 1,  # TODO: Get from auth context
    process_immediately: bool = True,
    options: Optional[str] = None,  # JSON string with processing options
    db: Session = Depends(get_db)
):
    """
    Upload a file for processing and analysis.
    Supports 30+ file formats including CSV, Excel, PDF, JSON, XML, Parquet, etc.
    """
    try:
        # Parse options if provided
        processing_options = {}
        if options:
            import json
            try:
                processing_options = json.loads(options)
            except json.JSONDecodeError:
                raise HTTPException(status_code=400, detail="Invalid options format")

        # Create file upload record
        file_record = models.FileUpload(
            id=str(uuid.uuid4()),
            filename=file.filename,
            file_size=0,  # Will be updated after reading
            file_type=file.content_type,
            organization_id=organization_id,
            uploaded_by=user_id,
            status="processing" if process_immediately else "pending",
            uploaded_at=datetime.utcnow()
        )
        db.add(file_record)
        db.commit()

        if process_immediately:
            # Process file immediately
            result = await file_processor.process_file(file, processing_options)

            # Update file record with results
            file_record.status = "completed"
            file_record.processed_at = datetime.utcnow()
            file_record.processing_result = result
            file_record.insights = result.get("insights", {})
            db.commit()

            # Create data source from processed file
            if result["status"] == "success":
                data_source = models.DataSource(
                    organization_id=organization_id,
                    name=f"Upload: {file.filename}",
                    source_type=models.DataSourceType.CSV,  # Default, should be mapped
                    connection_config={
                        "file_id": file_record.id,
                        "format": result["format"],
                        "metadata": result["metadata"]
                    },
                    is_active=True,
                    last_sync=datetime.utcnow(),
                    created_at=datetime.utcnow()
                )
                db.add(data_source)
                db.commit()

                result["data_source_id"] = data_source.id
                result["file_id"] = file_record.id

            return result
        else:
            # Queue for background processing
            background_tasks.add_task(
                process_file_background,
                file_record.id,
                await file.read(),
                file.filename,
                processing_options,
                db
            )

            return {
                "status": "queued",
                "file_id": file_record.id,
                "message": "File uploaded and queued for processing"
            }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error uploading file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error uploading file: {str(e)}")


@router.post("/upload/batch", response_model=List[Dict[str, Any]])
async def upload_multiple_files(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    organization_id: int = 1,  # TODO: Get from auth context
    user_id: int = 1,  # TODO: Get from auth context
    process_immediately: bool = False,
    db: Session = Depends(get_db)
):
    """
    Upload multiple files for batch processing.
    Files are processed in the background by default.
    """
    results = []

    for file in files:
        try:
            # Create file upload record
            file_record = models.FileUpload(
                id=str(uuid.uuid4()),
                filename=file.filename,
                file_size=0,
                file_type=file.content_type,
                organization_id=organization_id,
                uploaded_by=user_id,
                status="pending",
                uploaded_at=datetime.utcnow()
            )
            db.add(file_record)

            # Queue for background processing
            file_content = await file.read()
            background_tasks.add_task(
                process_file_background,
                file_record.id,
                file_content,
                file.filename,
                {},
                db
            )

            results.append({
                "file_id": file_record.id,
                "filename": file.filename,
                "status": "queued"
            })

        except Exception as e:
            logger.error(f"Error uploading file {file.filename}: {str(e)}")
            results.append({
                "filename": file.filename,
                "status": "error",
                "error": str(e)
            })

    db.commit()
    return results


@router.get("/upload/{file_id}/status")
async def get_file_status(
    file_id: str,
    db: Session = Depends(get_db)
):
    """
    Get the processing status of an uploaded file.
    """
    file_record = db.query(models.FileUpload).filter(
        models.FileUpload.id == file_id
    ).first()

    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")

    return {
        "file_id": file_record.id,
        "filename": file_record.filename,
        "status": file_record.status,
        "uploaded_at": file_record.uploaded_at,
        "processed_at": file_record.processed_at,
        "insights": file_record.insights,
        "error": file_record.error_message
    }


@router.get("/upload/{file_id}/results")
async def get_file_results(
    file_id: str,
    db: Session = Depends(get_db)
):
    """
    Get the processing results of an uploaded file.
    """
    file_record = db.query(models.FileUpload).filter(
        models.FileUpload.id == file_id
    ).first()

    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")

    if file_record.status != "completed":
        raise HTTPException(
            status_code=400,
            detail=f"File processing status: {file_record.status}"
        )

    return file_record.processing_result


@router.get("/history")
async def get_upload_history(
    skip: int = 0,
    limit: int = 10,
    organization_id: int = 1,  # TODO: Get from auth context
    db: Session = Depends(get_db)
):
    """
    Get the history of uploaded files for an organization.
    """
    files = db.query(models.FileUpload).filter(
        models.FileUpload.organization_id == organization_id
    ).order_by(
        models.FileUpload.uploaded_at.desc()
    ).offset(skip).limit(limit).all()

    return [
        {
            "id": f.id,
            "filename": f.filename,
            "size": f.file_size,
            "uploadDate": f.uploaded_at.isoformat() if f.uploaded_at else None,
            "status": f.status,
            "type": f.file_type,
            "processedRows": f.processing_result.get("metadata", {}).get("rows", 0) if f.processing_result else None,
            "errorMessage": f.error_message
        }
        for f in files
    ]


@router.get("/supported-formats")
async def get_supported_formats():
    """
    Get list of supported file formats and their capabilities.
    """
    return {
        "spreadsheets": {
            "formats": ["csv", "xlsx", "xls", "xlsm", "xlsb"],
            "description": "Tabular data with rows and columns",
            "capabilities": ["statistics", "preview", "column_analysis", "data_types"]
        },
        "documents": {
            "formats": ["pdf", "docx", "doc", "txt", "md", "rtf"],
            "description": "Text documents and reports",
            "capabilities": ["text_extraction", "table_extraction", "metadata"]
        },
        "data_formats": {
            "formats": ["json", "xml", "html", "yaml", "yml", "toml"],
            "description": "Structured data formats",
            "capabilities": ["structure_analysis", "schema_detection", "conversion"]
        },
        "binary_formats": {
            "formats": ["parquet", "feather", "hdf", "h5", "msgpack", "pickle", "pkl"],
            "description": "Binary data formats optimized for performance",
            "capabilities": ["fast_loading", "compression", "metadata"]
        },
        "databases": {
            "formats": ["sqlite", "db"],
            "description": "Database files",
            "capabilities": ["table_listing", "schema_extraction", "query_preview"]
        },
        "archives": {
            "formats": ["zip", "tar", "gz", "bz2"],
            "description": "Compressed archives containing multiple files",
            "capabilities": ["extraction", "file_listing", "nested_processing"]
        },
        "images": {
            "formats": ["png", "jpg", "jpeg", "tiff", "bmp"],
            "description": "Image files with OCR capabilities",
            "capabilities": ["text_extraction", "metadata", "ocr"]
        }
    }


@router.delete("/upload/{file_id}")
async def delete_uploaded_file(
    file_id: str,
    db: Session = Depends(get_db)
):
    """
    Delete an uploaded file and its associated data.
    """
    file_record = db.query(models.FileUpload).filter(
        models.FileUpload.id == file_id
    ).first()

    if not file_record:
        raise HTTPException(status_code=404, detail="File not found")

    # Delete associated data source if exists
    data_source = db.query(models.DataSource).filter(
        models.DataSource.connection_config["file_id"].astext == file_id
    ).first()

    if data_source:
        db.delete(data_source)

    db.delete(file_record)
    db.commit()

    return {"message": "File deleted successfully"}


async def process_file_background(
    file_id: str,
    file_content: bytes,
    filename: str,
    options: Dict[str, Any],
    db: Session
):
    """
    Background task to process uploaded files.
    """
    try:
        # Create a fake UploadFile object for the processor
        from io import BytesIO
        from tempfile import SpooledTemporaryFile

        file_like = SpooledTemporaryFile()
        file_like.write(file_content)
        file_like.seek(0)

        upload_file = UploadFile(
            filename=filename,
            file=file_like
        )

        # Process the file
        result = await file_processor.process_file(upload_file, options)

        # Update file record
        file_record = db.query(models.FileUpload).filter(
            models.FileUpload.id == file_id
        ).first()

        if file_record:
            file_record.status = "completed"
            file_record.processed_at = datetime.utcnow()
            file_record.processing_result = result
            file_record.insights = result.get("insights", {})
            file_record.file_size = len(file_content)
            db.commit()

            # Create data source if successful
            if result["status"] == "success":
                data_source = models.DataSource(
                    organization_id=file_record.organization_id,
                    name=f"Upload: {filename}",
                    source_type=models.DataSourceType.CSV,  # Should be mapped based on format
                    connection_config={
                        "file_id": file_id,
                        "format": result["format"],
                        "metadata": result["metadata"]
                    },
                    is_active=True,
                    last_sync=datetime.utcnow(),
                    created_at=datetime.utcnow()
                )
                db.add(data_source)
                db.commit()

    except Exception as e:
        logger.error(f"Error processing file {file_id}: {str(e)}")

        # Update file record with error
        file_record = db.query(models.FileUpload).filter(
            models.FileUpload.id == file_id
        ).first()

        if file_record:
            file_record.status = "error"
            file_record.error_message = str(e)
            db.commit()