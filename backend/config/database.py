"""
Database configuration for different environments
"""
import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from models.database import Base

def get_database_url(environment: str = None) -> str:
    """Get database URL for different environments"""
    if environment == "test":
        return "sqlite:///./test_dataflow.db"
    elif environment == "production":
        prod_url = os.getenv("PRODUCTION_DATABASE_URL")
        if not prod_url:
            raise ValueError("PRODUCTION_DATABASE_URL environment variable must be set for production")
        return prod_url
    else:
        # Development environment
        return os.getenv("DATABASE_URL", "sqlite:///./dataflow_pro.db")

def create_database_engine(environment: str = None):
    """Create database engine for different environments"""
    database_url = get_database_url(environment)

    if database_url.startswith("sqlite"):
        engine = create_engine(database_url, connect_args={"check_same_thread": False})
    else:
        engine = create_engine(database_url)

    return engine

def get_session_local(environment: str = None):
    """Get SessionLocal for different environments"""
    engine = create_database_engine(environment)
    return sessionmaker(autocommit=False, autoflush=False, bind=engine)

def create_tables(environment: str = None):
    """Create all tables for the given environment"""
    engine = create_database_engine(environment)
    Base.metadata.create_all(bind=engine)

def drop_tables(environment: str = None):
    """Drop all tables for the given environment"""
    engine = create_database_engine(environment)
    Base.metadata.drop_all(bind=engine)