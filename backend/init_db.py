#!/usr/bin/env python3
"""
Database initialization script for DataFlow Pro
Creates all tables and optionally seeds with sample data
"""

import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from models.database import engine, Base
from models.models import User, UserRole
from auth.security import get_password_hash
from sqlalchemy.orm import sessionmaker

def create_tables():
    """Create all database tables"""
    print("Creating database tables...")
    Base.metadata.create_all(bind=engine)
    print("✅ Database tables created successfully!")

def create_admin_user():
    """Create a default admin user for testing"""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        # Check if admin user already exists
        admin_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if admin_user:
            print("ℹ️  Admin user already exists")
            return

        # Create admin user
        admin_user = User(
            email="<EMAIL>",
            username="admin",
            full_name="Admin User",
            hashed_password=get_password_hash("admin123"),
            role=UserRole.ADMIN,
            is_active=True,
            is_verified=True
        )

        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)

        print(f"✅ Admin user created successfully!")
        print(f"   Email: <EMAIL>")
        print(f"   Password: admin123")
        print(f"   Please change the password after first login!")

    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        db.rollback()
    finally:
        db.close()

def create_test_user():
    """Create a test user for development"""
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()

    try:
        # Check if test user already exists
        test_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if test_user:
            print("ℹ️  Test user already exists")
            return

        # Create test user
        test_user = User(
            email="<EMAIL>",
            username="testuser",
            full_name="Test User",
            hashed_password=get_password_hash("password123"),
            role=UserRole.USER,
            is_active=True,
            is_verified=True
        )

        db.add(test_user)
        db.commit()
        db.refresh(test_user)

        print(f"✅ Test user created successfully!")
        print(f"   Email: <EMAIL>")
        print(f"   Password: password123")

    except Exception as e:
        print(f"❌ Error creating test user: {e}")
        db.rollback()
    finally:
        db.close()

def main():
    """Main initialization function"""
    print("🚀 Initializing DataFlow Pro Database...")
    print("=" * 50)

    try:
        # Create tables
        create_tables()

        # Create sample users
        create_admin_user()
        create_test_user()

        print("=" * 50)
        print("✅ Database initialization completed successfully!")
        print()
        print("You can now start the server with:")
        print("  source venv/bin/activate")
        print("  uvicorn main:app --reload")
        print()
        print("API Documentation will be available at:")
        print("  http://localhost:8000/docs")

    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())