#!/usr/bin/env python3
"""
Test script for email functionality
Run this to test email sending in development mode
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

from services.email_service import EmailService, EmailConfig


async def test_email_service():
    """Test the email service functionality"""
    print("🧪 Testing Email Service")
    print("=" * 50)
    
    # Create email service instance
    config = EmailConfig()
    email_service = EmailService(config)
    
    # Test data
    test_email = "<EMAIL>"
    test_name = "Test User"
    test_token = "test-token-123"
    
    print(f"Environment: {config.environment}")
    print(f"SMTP Host: {config.smtp_host}")
    print(f"From Email: {config.from_email}")
    print(f"Base URL: {config.base_url}")
    print()
    
    # Test verification email
    print("📧 Testing verification email...")
    success = await email_service.send_verification_email(
        user_email=test_email,
        user_name=test_name,
        verification_token=test_token
    )
    print(f"Verification email sent: {success}")
    print()
    
    # Test password reset email
    print("🔐 Testing password reset email...")
    success = await email_service.send_password_reset_email(
        user_email=test_email,
        user_name=test_name,
        reset_token=test_token
    )
    print(f"Password reset email sent: {success}")
    print()
    
    # Test welcome email
    print("🎉 Testing welcome email...")
    success = await email_service.send_welcome_email(
        user_email=test_email,
        user_name=test_name
    )
    print(f"Welcome email sent: {success}")
    print()
    
    print("✅ Email service test completed!")


async def test_smtp_config():
    """Test SMTP configuration"""
    print("\n🔧 Testing SMTP Configuration")
    print("=" * 50)
    
    config = EmailConfig()
    
    # Check if SMTP credentials are configured
    if config.smtp_username and config.smtp_password:
        print("✅ SMTP credentials are configured")
        print(f"   Username: {config.smtp_username}")
        print(f"   Host: {config.smtp_host}:{config.smtp_port}")
        print(f"   TLS: {config.smtp_use_tls}")
        
        # Test actual SMTP connection (optional)
        try:
            import aiosmtplib
            
            print("\n🔌 Testing SMTP connection...")
            
            # Create a simple test connection
            smtp = aiosmtplib.SMTP(
                hostname=config.smtp_host,
                port=config.smtp_port,
                start_tls=config.smtp_use_tls
            )
            
            await smtp.connect()
            await smtp.login(config.smtp_username, config.smtp_password)
            await smtp.quit()
            
            print("✅ SMTP connection successful!")
            
        except Exception as e:
            print(f"❌ SMTP connection failed: {e}")
            print("   This is normal in development if credentials aren't configured")
    else:
        print("⚠️  SMTP credentials not configured")
        print("   Emails will be displayed in console (development mode)")


def main():
    """Main test function"""
    print("DataFlow Pro - Email Service Test")
    print("=" * 50)
    
    # Run async tests
    asyncio.run(test_email_service())
    asyncio.run(test_smtp_config())
    
    print("\n" + "=" * 50)
    print("📝 Notes:")
    print("- In development mode, emails are printed to console")
    print("- Configure SMTP settings in .env file for production")
    print("- Email templates are created automatically in templates/email/")
    print("- Check the authentication endpoints to see email integration")


if __name__ == "__main__":
    main()
