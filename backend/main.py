from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import uvicorn
from dotenv import load_dotenv
import os

# Import routers
from routers import dashboard, dashboards, insights, etl, collaboration, file_upload, auth, data_sources, etl_pipelines, notifications

load_dotenv()

@asynccontextmanager
async def lifespan(app: FastAPI):
    print("Starting up DataFlow Pro API...")
    # Here you could initialize database, create tables, etc.
    yield
    print("Shutting down...")

app = FastAPI(
    title="DataFlow Pro API",
    description="Advanced analytics platform API with AI-powered insights",
    version="1.0.0",
    lifespan=lifespan
)

origins = [
    "http://localhost:3000",
    "http://localhost:3001",
    "http://127.0.0.1:3000",
    "http://127.0.0.1:3001",
    "*"  # For development - remove in production
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins in development
    allow_credentials=False,  # Set to False for development
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"],
)

# Include routers
app.include_router(auth.router)
# app.include_router(dashboard.router)  # Temporarily disabled for Dashboard Builder development
app.include_router(dashboards.router)  # New Dashboard Builder router
app.include_router(insights.router)
app.include_router(etl.router)
app.include_router(collaboration.router)
app.include_router(file_upload.router)
app.include_router(data_sources.router)
app.include_router(etl_pipelines.router)
app.include_router(notifications.router)

@app.get("/")
async def root():
    return {
        "message": "Welcome to DataFlow Pro API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "service": "dataflow-pro-backend",
        "version": "1.0.0"
    }

@app.get("/api/v1/test")
async def test_endpoint():
    return {"message": "Test endpoint working", "data": {"test": True}}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True
    )