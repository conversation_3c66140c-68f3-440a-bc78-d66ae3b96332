# Email Verification System

This document describes the email verification system implemented in DataFlow Pro.

## Overview

The email verification system provides:
- ✅ Email verification for new user registrations
- 🔐 Password reset via email
- 🎉 Welcome emails after successful verification
- 📧 Resend verification emails
- 🔧 Development and production email modes

## Features

### 1. User Registration with Email Verification
- New users receive a verification email upon registration
- Users must verify their email before full account activation
- Verification tokens expire after 24 hours

### 2. Password Reset
- Users can request password reset via email
- Reset tokens expire after 1 hour
- Secure token-based reset process

### 3. Welcome Emails
- Sent automatically after successful email verification
- Includes links to dashboard and key features

### 4. Email Templates
- Professional HTML email templates
- Responsive design for all devices
- Consistent branding with DataFlow Pro

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true
FROM_EMAIL=<EMAIL>
FROM_NAME=DataFlow Pro
BASE_URL=http://localhost:3000
ENVIRONMENT=development
```

### Gmail Setup (Recommended)

1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate password for "Mail"
3. Use the app password as `SMTP_PASSWORD`

### Other Email Providers

The system supports any SMTP provider. Common configurations:

**Outlook/Hotmail:**
```bash
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
```

**Yahoo:**
```bash
SMTP_HOST=smtp.mail.yahoo.com
SMTP_PORT=587
```

**SendGrid:**
```bash
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USERNAME=apikey
SMTP_PASSWORD=your-sendgrid-api-key
```

## Development Mode

In development (`ENVIRONMENT=development`), emails are printed to the console instead of being sent. This allows testing without SMTP configuration.

Example console output:
```
============================================================
📧 DEVELOPMENT EMAIL
============================================================
To: <EMAIL>
From: DataFlow Pro <<EMAIL>>
Subject: Verify Your Email - DataFlow Pro
------------------------------------------------------------
HTML CONTENT:
[Email HTML content displayed here]
============================================================
```

## API Endpoints

### Registration with Email Verification
```http
POST /api/v1/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "testuser",
  "full_name": "Test User",
  "password": "SecurePass123!"
}
```

### Verify Email
```http
POST /api/v1/auth/verify-email
Content-Type: application/json

{
  "token": "verification-token-from-email"
}
```

### Request Password Reset
```http
POST /api/v1/auth/request-password-reset
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

### Reset Password
```http
POST /api/v1/auth/reset-password
Content-Type: application/json

{
  "token": "reset-token-from-email",
  "new_password": "NewSecurePass123!"
}
```

### Resend Verification Email
```http
POST /api/v1/auth/resend-verification
Authorization: Bearer your-jwt-token
```

## Email Templates

Templates are located in `backend/templates/email/`:

- `email_verification.html` - Email verification template
- `password_reset.html` - Password reset template
- Welcome email is generated dynamically

Templates use Jinja2 templating with these variables:
- `app_name` - Application name
- `user_name` - User's display name
- `verification_url` - Verification link
- `reset_url` - Password reset link
- `base_url` - Application base URL

## Testing

### Test Email Service
```bash
cd backend
python test_email.py
```

This will:
- Test all email types in development mode
- Verify SMTP configuration (if configured)
- Display sample emails in console

### Manual Testing
1. Register a new user via API
2. Check console output for verification email
3. Extract token from email content
4. Call verify-email endpoint with token
5. Check for welcome email in console

## Security Features

### Token Security
- JWT tokens with expiration times
- Separate token types for verification vs password reset
- Tokens include email and type in payload

### Email Security
- Always return success for password reset requests (prevents email enumeration)
- Verification tokens expire after 24 hours
- Reset tokens expire after 1 hour
- Secure token generation using JOSE library

### Rate Limiting (Recommended)
Consider implementing rate limiting for:
- Registration attempts
- Password reset requests
- Verification email resends

## Troubleshooting

### Common Issues

**Emails not sending in production:**
- Check SMTP credentials in environment variables
- Verify SMTP host and port settings
- Check firewall/network restrictions
- Test SMTP connection with `python test_email.py`

**Gmail authentication errors:**
- Ensure 2FA is enabled
- Use App Password, not regular password
- Check "Less secure app access" if not using App Password

**Template errors:**
- Templates are created automatically if missing
- Check `backend/templates/email/` directory exists
- Verify Jinja2 is installed

**Token verification fails:**
- Check SECRET_KEY is consistent
- Verify token hasn't expired
- Ensure token type matches endpoint

### Logs

Enable debug logging to troubleshoot:
```bash
LOG_LEVEL=DEBUG
```

Check logs for:
- Email sending attempts
- SMTP connection errors
- Template rendering issues
- Token generation/verification

## Production Deployment

### Checklist
- [ ] Configure production SMTP settings
- [ ] Set `ENVIRONMENT=production`
- [ ] Use secure SECRET_KEY
- [ ] Configure proper BASE_URL
- [ ] Test email delivery
- [ ] Set up monitoring for email failures
- [ ] Consider using dedicated email service (SendGrid, AWS SES)

### Monitoring
Monitor these metrics:
- Email delivery success rate
- Token verification rate
- Failed SMTP connections
- Template rendering errors

## Future Enhancements

Potential improvements:
- [ ] Email delivery status tracking
- [ ] Email templates in database
- [ ] Multi-language email support
- [ ] Email analytics and metrics
- [ ] Bulk email capabilities
- [ ] Email queue for high volume
- [ ] Integration with email services (SendGrid, Mailgun)
