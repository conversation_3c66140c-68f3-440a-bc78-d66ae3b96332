from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from enum import Enum


class NotificationTypeEnum(str, Enum):
    SYSTEM = "system"
    AUTH = "auth"
    DATA = "data"
    ETL = "etl"
    AI_INSIGHT = "ai_insight"
    COLLABORATION = "collaboration"
    SECURITY = "security"
    PERFORMANCE = "performance"
    BILLING = "billing"
    USER_ACTION = "user_action"


class NotificationSeverityEnum(str, Enum):
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"


class NotificationStatusEnum(str, Enum):
    UNREAD = "unread"
    READ = "read"
    ARCHIVED = "archived"


class NotificationBase(BaseModel):
    type: NotificationTypeEnum
    severity: NotificationSeverityEnum = NotificationSeverityEnum.INFO
    title: str = Field(..., max_length=255)
    message: str
    details: Optional[Dict[str, Any]] = None
    action_url: Optional[str] = Field(None, max_length=500)
    action_label: Optional[str] = Field(None, max_length=100)
    related_entity_type: Optional[str] = Field(None, max_length=50)
    related_entity_id: Optional[str] = Field(None, max_length=100)
    persistent: bool = False
    show_browser: bool = True
    play_sound: bool = False
    priority: int = 0
    expires_at: Optional[datetime] = None


class NotificationCreate(NotificationBase):
    user_id: Optional[int] = None  # Can be set by the system
    organization_id: Optional[int] = None


class NotificationUpdate(BaseModel):
    status: Optional[NotificationStatusEnum] = None
    read_at: Optional[datetime] = None
    archived_at: Optional[datetime] = None


class NotificationResponse(NotificationBase):
    id: str
    user_id: int
    organization_id: Optional[int] = None
    status: NotificationStatusEnum
    created_at: datetime
    read_at: Optional[datetime] = None
    archived_at: Optional[datetime] = None

    class Config:
        orm_mode = True


class NotificationListResponse(BaseModel):
    notifications: List[NotificationResponse]
    total: int
    unread_count: int
    page: int = 1
    page_size: int = 50


class NotificationMarkAsReadRequest(BaseModel):
    notification_ids: List[str]


class NotificationBulkCreateRequest(BaseModel):
    notifications: List[NotificationCreate]
    target_users: Optional[List[int]] = None  # If specified, create for these users
    target_organization_id: Optional[int] = None  # If specified, create for all users in org


class NotificationFilterRequest(BaseModel):
    types: Optional[List[NotificationTypeEnum]] = None
    severities: Optional[List[NotificationSeverityEnum]] = None
    status: Optional[NotificationStatusEnum] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    search_query: Optional[str] = None