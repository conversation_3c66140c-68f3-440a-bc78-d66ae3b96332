from pydantic import BaseModel, EmailStr, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

# Enums (matching the SQLAlchemy models)
class UserRole(str, Enum):
    ADMIN = "admin"
    USER = "user"
    PARTNER = "partner"
    VIEWER = "viewer"

class DataSourceType(str, Enum):
    SALESFORCE = "salesforce"
    HUBSPOT = "hubspot"
    GOOGLE_ANALYTICS = "google_analytics"
    FACEBOOK_ADS = "facebook_ads"
    QUICKBOOKS = "quickbooks"
    STRIPE = "stripe"
    CUSTOM_API = "custom_api"
    DATABASE = "database"
    CSV = "csv"

class PipelineStatus(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PROCESSING = "processing"

class InsightType(str, Enum):
    ANOMALY = "anomaly"
    PREDICTION = "prediction"
    OPTIMIZATION = "optimization"
    TREND = "trend"

class InsightPriority(str, Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class RunStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

# User Schemas
class UserBase(BaseModel):
    email: EmailStr
    username: str
    full_name: Optional[str] = None
    role: UserRole = UserRole.USER
    avatar_url: Optional[str] = None

class UserCreate(UserBase):
    password: str

class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    avatar_url: Optional[str] = None

class User(UserBase):
    id: int
    is_active: bool
    is_verified: bool
    created_at: datetime
    updated_at: Optional[datetime]

    model_config = ConfigDict(from_attributes=True)

# Dashboard Schemas
class DashboardBase(BaseModel):
    name: str
    description: Optional[str] = None
    is_public: bool = False
    is_template: bool = False
    template_category: Optional[str] = None
    configuration: Optional[Dict[str, Any]] = None

class DashboardCreate(DashboardBase):
    organization_id: int

class DashboardUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_public: Optional[bool] = None
    configuration: Optional[Dict[str, Any]] = None

class Dashboard(DashboardBase):
    id: int
    organization_id: int
    owner_id: int
    created_at: datetime
    updated_at: Optional[datetime]

    model_config = ConfigDict(from_attributes=True)

# Dashboard Statistics Schema
class DashboardStats(BaseModel):
    monthly_active_users: int
    connected_sources: int
    records_processed: int
    data_quality_score: float
    user_growth: float
    source_growth: int
    processing_growth: float
    quality_improvement: float

# Metric Schema
class MetricBase(BaseModel):
    name: str
    value: float
    unit: Optional[str] = None
    timestamp: datetime
    tags: Optional[Dict[str, str]] = None

# Widget Schemas
class WidgetBase(BaseModel):
    widget_type: str
    title: str
    configuration: Dict[str, Any]
    position: Dict[str, float]  # x, y, width, height

class WidgetCreate(WidgetBase):
    dashboard_id: int
    data_source_id: Optional[int] = None

class WidgetCreateRequest(WidgetBase):
    data_source_id: Optional[int] = None

class WidgetUpdate(BaseModel):
    widget_type: Optional[str] = None
    title: Optional[str] = None
    configuration: Optional[Dict[str, Any]] = None
    position: Optional[Dict[str, float]] = None
    data_source_id: Optional[int] = None

class Widget(WidgetBase):
    id: int
    dashboard_id: int
    data_source_id: Optional[int]
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Data Source Schemas
class DataSourceBase(BaseModel):
    name: str
    source_type: DataSourceType
    connection_config: Dict[str, Any]
    sync_frequency: Optional[str] = None

class DataSourceCreate(DataSourceBase):
    organization_id: int

class DataSourceUpdate(BaseModel):
    name: Optional[str] = None
    source_type: Optional[DataSourceType] = None
    connection_config: Optional[Dict[str, Any]] = None
    sync_frequency: Optional[str] = None
    is_active: Optional[bool] = None

class DataSource(DataSourceBase):
    id: int
    organization_id: int
    is_active: bool
    last_sync: Optional[datetime]
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# ETL Pipeline Schemas
class ETLPipelineBase(BaseModel):
    name: str
    description: Optional[str] = None
    destination_config: Dict[str, Any]
    transformation_rules: Dict[str, Any]
    schedule: str

class ETLPipelineCreate(ETLPipelineBase):
    source_id: int

class ETLPipeline(ETLPipelineBase):
    id: int
    source_id: int
    status: PipelineStatus
    last_run: Optional[datetime]
    next_run: Optional[datetime]
    records_processed: int
    success_rate: float
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# AI Insight Schemas
class AIInsightBase(BaseModel):
    insight_type: InsightType
    priority: InsightPriority
    title: str
    description: str
    confidence_score: float = Field(ge=0, le=1)
    data_source: str
    affected_metrics: Dict[str, Any]
    recommendations: List[str]

class AIInsightCreate(AIInsightBase):
    user_id: int

class AIInsight(AIInsightBase):
    id: int
    user_id: int
    is_resolved: bool
    created_at: datetime
    resolved_at: Optional[datetime]

    model_config = ConfigDict(from_attributes=True)

# Predictive Model Schemas
class PredictiveModelBase(BaseModel):
    name: str
    model_type: str
    accuracy: float = Field(ge=0, le=1)
    confidence_threshold: float = Field(ge=0, le=1)
    features_used: List[str]
    training_data_config: Dict[str, Any]

class PredictiveModelCreate(PredictiveModelBase):
    pass

class PredictiveModel(PredictiveModelBase):
    id: int
    last_trained: Optional[datetime]
    is_active: bool
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Team Workspace Schemas
class TeamWorkspaceBase(BaseModel):
    name: str
    description: Optional[str] = None

class TeamWorkspaceCreate(TeamWorkspaceBase):
    organization_id: int

class TeamWorkspace(TeamWorkspaceBase):
    id: int
    organization_id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Comment Schemas
class CommentBase(BaseModel):
    content: str

class CommentCreate(CommentBase):
    workspace_id: int
    widget_id: Optional[int] = None

class Comment(CommentBase):
    id: int
    workspace_id: int
    user_id: int
    widget_id: Optional[int]
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Partner Config Schemas
class PartnerConfigBase(BaseModel):
    custom_domain: Optional[str] = None
    logo_url: Optional[str] = None
    primary_color: str = "#1e40af"
    secondary_color: str = "#3b82f6"
    custom_css: Optional[str] = None
    revenue_share_percentage: float = Field(ge=0, le=100)

class PartnerConfigCreate(PartnerConfigBase):
    organization_id: int

class PartnerConfig(PartnerConfigBase):
    id: int
    organization_id: int
    monthly_customers: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Cost Tracking Schemas
class CostTrackingBase(BaseModel):
    category: str
    amount: float
    period: datetime
    trend: str
    optimization_suggestions: List[Dict[str, Any]]

class CostTrackingCreate(CostTrackingBase):
    organization_id: int

class CostTracking(CostTrackingBase):
    id: int
    organization_id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Audit Log Schemas
class AuditLogBase(BaseModel):
    action: str
    resource_type: str
    resource_id: int
    action_metadata: Optional[Dict[str, Any]] = None  # Changed from metadata to action_metadata

class AuditLogCreate(AuditLogBase):
    user_id: int
    ip_address: str
    user_agent: str

class AuditLog(AuditLogBase):
    id: int
    user_id: int
    ip_address: str
    user_agent: str
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Compliance Report Schemas
class ComplianceReportBase(BaseModel):
    compliance_type: str
    status: str
    findings: Dict[str, Any]
    recommendations: List[str]

class ComplianceReportCreate(ComplianceReportBase):
    organization_id: int

class ComplianceReport(ComplianceReportBase):
    id: int
    organization_id: int
    generated_at: datetime

    model_config = ConfigDict(from_attributes=True)

# Metric Schemas
class MetricBase(BaseModel):
    name: str
    value: float
    unit: str
    change_percentage: float
    change_direction: str

class MetricCreate(MetricBase):
    organization_id: int

class Metric(MetricBase):
    id: int
    organization_id: int
    timestamp: datetime

    model_config = ConfigDict(from_attributes=True)

# Dashboard Statistics Response
class DashboardStats(BaseModel):
    monthly_active_users: int
    connected_sources: int
    records_processed: int
    data_quality_score: float
    user_growth: float
    source_growth: int
    processing_growth: float
    quality_improvement: float

# Revenue Prediction Response
class RevenuePrediction(BaseModel):
    current_revenue: float
    predicted_revenue: float
    growth_percentage: float
    confidence_score: float
    timeframe: str

# Health Check Response
class HealthCheck(BaseModel):
    status: str = "healthy"
    service: str = "fastapi-backend"
    timestamp: datetime
    database: bool
    cache: bool
    version: str = "1.0.0"

# ETL Pipeline Schemas
class ETLPipelineBase(BaseModel):
    name: str
    organization_id: int
    source_id: int
    destination_config: Dict[str, Any]
    transformation_rules: Dict[str, Any]
    schedule: Optional[str] = None

class ETLPipelineCreate(ETLPipelineBase):
    pass

class ETLPipelineUpdate(BaseModel):
    name: Optional[str] = None
    destination_config: Optional[Dict[str, Any]] = None
    transformation_rules: Optional[Dict[str, Any]] = None
    schedule: Optional[str] = None
    status: Optional[str] = None

class ETLPipelineResponse(ETLPipelineBase):
    id: int
    status: str
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)

class PipelineRunResponse(BaseModel):
    run_id: str
    pipeline_id: int
    status: str
    started_at: datetime
    finished_at: Optional[datetime] = None
    run_metadata: Optional[Dict[str, Any]] = None

    model_config = ConfigDict(from_attributes=True)

class TransformationRequest(BaseModel):
    transformation: Dict[str, Any]
    data: Any

class TransformationResponse(BaseModel):
    transformed_data: Any
    transformation_applied: Dict[str, Any]

class PipelineValidationRequest(BaseModel):
    name: str
    organization_id: int
    source_id: int
    destination_config: Dict[str, Any]
    transformation_rules: Dict[str, Any]
    schedule: Optional[str] = None

class PipelineValidationResponse(BaseModel):
    is_valid: bool
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None

# Paginated Response Schema
class PaginatedDashboardResponse(BaseModel):
    total: int
    page: int
    limit: int
    items: List[Dashboard]