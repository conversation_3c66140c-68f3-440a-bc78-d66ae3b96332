[project]
name = "dataflow-pro"
version = "1.0.0"
description = "Enterprise Analytics Platform for SMEs - Democratizing Data Intelligence"
authors = [
    { name = "DataFlow Pro Team", email = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.11"
license = { text = "MIT" }

dependencies = [
    # Core FastAPI
    "fastapi==0.115.6",
    "uvicorn[standard]==0.34.0",
    "pydantic==2.10.4",
    "python-dotenv==1.0.1",
    # Database
    "sqlalchemy==2.0.36",
    "psycopg2-binary==2.9.10",
    "alembic==1.14.0",
    "redis==5.0.1",
    # Authentication & Security
    "python-jose[cryptography]==3.3.0",
    "passlib[bcrypt]==1.7.4",
    "python-multipart==0.0.20",
    "email-validator==2.2.0",
    # File Processing
    "pandas==2.2.0",
    "openpyxl==3.1.2", # Excel files
    "xlrd==2.0.1", # Old Excel files
    "pdfplumber==0.10.3", # PDF processing
    "python-docx==1.1.0", # Word documents
    "markdown==3.5.2", # Markdown files
    "pyarrow==15.0.0", # Parquet files
    "lxml==5.1.0", # XML processing
    "beautifulsoup4==4.12.3", # HTML parsing
    "python-pptx==0.6.23", # PowerPoint files
    "Pillow==10.2.0", # Image processing
    "pytesseract==0.3.10", # OCR for images
    "pyyaml==6.0.1", # YAML files
    "toml==0.10.2", # TOML files
    "avro==1.11.3", # Avro files
    "msgpack==1.0.7", # MessagePack files
    "h5py==3.10.0", # HDF5 files
    # Data Analysis & AI
    "numpy==1.26.3",
    "scipy==1.12.0",
    "scikit-learn==1.4.0",
    "statsmodels==0.14.1",
    "prophet==1.1.5", # Time series forecasting
    # ETL & Processing
    "celery==5.3.6",
    "kombu==5.3.5",
    # HTTP & Networking
    "httpx==0.28.1",
    "aiofiles==23.2.1",
    "websockets==12.0",
    # Monitoring & Logging
    "prometheus-client==0.19.0",
    "sentry-sdk==1.40.0",
    "structlog==24.1.0",
    # Testing
    "pytest==8.3.4",
    "pytest-asyncio==0.25.0",
    "pytest-cov==4.1.0",
    "faker==22.5.1",
    "factory-boy==3.3.0",
]

[project.optional-dependencies]
dev = [
    "black==24.1.1",
    "ruff==0.1.14",
    "mypy==1.8.0",
    "pre-commit==3.6.0",
    "ipython==8.20.0",
]

analytics = [
    "clickhouse-driver==0.2.6",
    "timescaledb==0.2.0",
    "influxdb-client==1.39.0",
]

ml = [
    "tensorflow==2.15.0",
    "torch==2.2.0",
    "transformers==4.37.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
# UV specific settings for faster package resolution
resolution = "highest"
prerelease = "disallow"
cache-dir = ".uv-cache"

[tool.uv.pip]
# Pip compatibility settings
index-url = "https://pypi.org/simple"
extra-index-url = []
find-links = []

[tool.ruff]
target-version = "py311"
line-length = 100
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
]
ignore = []
fixable = ["ALL"]
unfixable = []
exclude = [
    ".git",
    ".ruff_cache",
    ".uv-cache",
    "__pycache__",
    "migrations",
    "venv",
]

[tool.black]
line-length = 100
target-version = ["py311"]
include = '\.pyi?$'
exclude = '''
/(
    \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | _build
  | buck-out
  | build
  | dist
  | migrations
)/'''

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --ignore=migrations"
testpaths = [
    "tests",
]
pythonpath = [
    "."
]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true
exclude = [
    "migrations/",
    "venv/",
    ".uv-cache/",
]
