["tests/test_auth.py::TestAuthenticatedEndpoints::test_protected_endpoint_with_invalid_token", "tests/test_auth.py::TestAuthenticatedEndpoints::test_protected_endpoint_with_valid_token", "tests/test_auth.py::TestAuthenticatedEndpoints::test_protected_endpoint_without_token", "tests/test_auth.py::TestEmailValidation::test_email_confirmation_endpoint", "tests/test_auth.py::TestEmailValidation::test_email_validation_token_generation", "tests/test_auth.py::TestJWTTokens::test_create_access_token", "tests/test_auth.py::TestJWTTokens::test_decode_access_token", "tests/test_auth.py::TestPasswordHashing::test_password_hashing", "tests/test_auth.py::TestPasswordHashing::test_password_strength_validation", "tests/test_auth.py::TestUserLogin::test_user_login_invalid_credentials", "tests/test_auth.py::TestUserLogin::test_user_login_success", "tests/test_auth.py::TestUserLogin::test_user_login_with_username", "tests/test_auth.py::TestUserLogin::test_user_login_wrong_password", "tests/test_auth.py::TestUserRegistration::test_user_registration_duplicate_email", "tests/test_auth.py::TestUserRegistration::test_user_registration_duplicate_username", "tests/test_auth.py::TestUserRegistration::test_user_registration_invalid_email", "tests/test_auth.py::TestUserRegistration::test_user_registration_success", "tests/test_auth.py::TestUserRegistration::test_user_registration_weak_password", "tests/test_dashboards.py::TestDashboardCRUD::test_create_dashboard_invalid_layout", "tests/test_dashboards.py::TestDashboardCRUD::test_create_dashboard_success", "tests/test_dashboards.py::TestDashboardCRUD::test_delete_dashboard", "tests/test_dashboards.py::TestDashboardCRUD::test_get_dashboard_by_id", "tests/test_dashboards.py::TestDashboardCRUD::test_get_dashboard_not_found", "tests/test_dashboards.py::TestDashboardCRUD::test_list_dashboards", "tests/test_dashboards.py::TestDashboardCRUD::test_update_dashboard", "tests/test_dashboards.py::TestDashboardExport::test_duplicate_dashboard", "tests/test_dashboards.py::TestDashboardExport::test_export_dashboard_json", "tests/test_dashboards.py::TestDashboardSharing::test_generate_public_link", "tests/test_dashboards.py::TestDashboardSharing::test_share_dashboard", "tests/test_dashboards.py::TestDashboardTemplates::test_create_dashboard_from_template", "tests/test_dashboards.py::TestDashboardTemplates::test_create_template", "tests/test_dashboards.py::TestDashboardTemplates::test_list_templates", "tests/test_dashboards.py::TestDashboardVersioning::test_list_dashboard_versions", "tests/test_dashboards.py::TestDashboardVersioning::test_restore_dashboard_version", "tests/test_dashboards.py::TestDashboardVersioning::test_save_dashboard_version", "tests/test_dashboards.py::TestWidgetManagement::test_add_widget_invalid_position", "tests/test_dashboards.py::TestWidgetManagement::test_add_widget_to_dashboard", "tests/test_dashboards.py::TestWidgetManagement::test_delete_widget", "tests/test_dashboards.py::TestWidgetManagement::test_reorder_widgets", "tests/test_dashboards.py::TestWidgetManagement::test_update_widget", "tests/test_data_sources.py::TestDataSourceConnections::test_connection_retry_mechanism", "tests/test_data_sources.py::TestDataSourceConnections::test_connection_test_google_analytics", "tests/test_data_sources.py::TestDataSourceConnections::test_connection_test_hubspot", "tests/test_data_sources.py::TestDataSourceConnections::test_connection_test_salesforce", "tests/test_data_sources.py::TestDataSourceConnections::test_credential_deletion", "tests/test_data_sources.py::TestDataSourceConnections::test_credential_storage_encryption", "tests/test_data_sources.py::TestDataSourceConnections::test_credential_update", "tests/test_data_sources.py::TestDataSourceConnections::test_get_single_data_source", "tests/test_data_sources.py::TestDataSourceConnections::test_google_analytics_oauth_callback", "tests/test_data_sources.py::TestDataSourceConnections::test_google_analytics_oauth_initiate", "tests/test_data_sources.py::TestDataSourceConnections::test_hubspot_oauth_callback", "tests/test_data_sources.py::TestDataSourceConnections::test_hubspot_oauth_initiate", "tests/test_data_sources.py::TestDataSourceConnections::test_list_data_sources", "tests/test_data_sources.py::TestDataSourceConnections::test_oauth_error_handling", "tests/test_data_sources.py::TestDataSourceConnections::test_salesforce_oauth_callback", "tests/test_data_sources.py::TestDataSourceConnections::test_salesforce_oauth_initiate", "tests/test_data_sources.py::TestDataSourceConnections::test_update_data_source_status", "tests/test_data_sources.py::TestOAuthTokenManagement::test_automatic_token_refresh_on_expiry", "tests/test_data_sources.py::TestOAuthTokenManagement::test_google_analytics_token_refresh", "tests/test_data_sources.py::TestOAuthTokenManagement::test_hubspot_token_refresh", "tests/test_data_sources.py::TestOAuthTokenManagement::test_salesforce_token_refresh", "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_create_etl_pipeline", "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_delete_pipeline", "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_get_pipeline_runs", "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_get_single_pipeline", "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_list_pipelines", "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_pause_pipeline", "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_resume_pipeline", "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_run_pipeline_manually", "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_update_pipeline", "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_validate_transformation_rules", "tests/test_etl_pipeline.py::TestTransformationEngine::test_aggregation_transformation", "tests/test_etl_pipeline.py::TestTransformationEngine::test_field_mapping_transformation", "tests/test_etl_pipeline.py::TestTransformationEngine::test_filter_transformation"]