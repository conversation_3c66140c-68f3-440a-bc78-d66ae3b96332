{"tests/test_data_sources.py::TestDataSourceConnections::test_connection_test_salesforce": true, "tests/test_data_sources.py::TestDataSourceConnections::test_connection_test_hubspot": true, "tests/test_data_sources.py::TestDataSourceConnections::test_connection_test_google_analytics": true, "tests/test_data_sources.py::TestDataSourceConnections::test_credential_storage_encryption": true, "tests/test_data_sources.py::TestDataSourceConnections::test_credential_update": true, "tests/test_data_sources.py::TestDataSourceConnections::test_credential_deletion": true, "tests/test_data_sources.py::TestDataSourceConnections::test_connection_retry_mechanism": true, "tests/test_data_sources.py::TestDataSourceConnections::test_list_data_sources": true, "tests/test_data_sources.py::TestDataSourceConnections::test_get_single_data_source": true, "tests/test_data_sources.py::TestDataSourceConnections::test_update_data_source_status": true, "tests/test_data_sources.py::TestOAuthTokenManagement::test_salesforce_token_refresh": true, "tests/test_data_sources.py::TestOAuthTokenManagement::test_hubspot_token_refresh": true, "tests/test_data_sources.py::TestOAuthTokenManagement::test_google_analytics_token_refresh": true, "tests/test_data_sources.py::TestOAuthTokenManagement::test_automatic_token_refresh_on_expiry": true, "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_create_etl_pipeline": true, "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_delete_pipeline": true, "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_get_pipeline_runs": true, "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_get_single_pipeline": true, "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_pause_pipeline": true, "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_resume_pipeline": true, "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_run_pipeline_manually": true, "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_update_pipeline": true, "tests/test_etl_pipeline.py::TestTransformationEngine::test_field_mapping_transformation": true, "tests/test_etl_pipeline.py::TestETLPipelineBuilder::test_list_pipelines": true, "tests/test_dashboards.py::TestDashboardTemplates::test_create_template": true, "tests/test_dashboards.py::TestDashboardTemplates::test_list_templates": true, "tests/test_dashboards.py::TestDashboardTemplates::test_create_dashboard_from_template": true, "tests/test_dashboards.py::TestDashboardVersioning::test_save_dashboard_version": true, "tests/test_dashboards.py::TestDashboardVersioning::test_restore_dashboard_version": true, "tests/test_dashboards.py::TestDashboardExport::test_export_dashboard_json": true, "tests/test_dashboards.py::TestDashboardExport::test_duplicate_dashboard": true, "tests/test_dashboards.py::TestDashboardCRUD::test_create_dashboard_success": true}