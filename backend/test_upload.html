<!DOCTYPE html>
<html>
<head>
    <title>File Upload Test</title>
</head>
<body>
    <h1>File Upload Test</h1>
    <input type="file" id="fileInput" accept=".csv">
    <button onclick="uploadFile()">Upload</button>
    <div id="result"></div>

    <script>
        async function uploadFile() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Please select a file');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('process_immediately', 'true');

            try {
                const response = await fetch('http://localhost:8000/api/v1/files/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                document.getElementById('result').innerHTML = `
                    <h3>Upload Successful!</h3>
                    <pre>${JSON.stringify(result, null, 2)}</pre>
                `;
            } catch (error) {
                document.getElementById('result').innerHTML = `
                    <h3>Upload Failed</h3>
                    <p>${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
