from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Float, <PERSON><PERSON><PERSON>, DateT<PERSON>, Foreign<PERSON>ey, Text, JSON, Enum as SQLEnum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from models.database import Base
import enum
from datetime import datetime

# Enums
class UserRole(enum.Enum):
    ADMIN = "admin"
    USER = "user"
    PARTNER = "partner"
    VIEWER = "viewer"

class DataSourceType(enum.Enum):
    SALESFORCE = "salesforce"
    HUBSPOT = "hubspot"
    GOOGLE_ANALYTICS = "google_analytics"
    FACEBOOK_ADS = "facebook_ads"
    QUICKBOOKS = "quickbooks"
    STRIPE = "stripe"
    CUSTOM_API = "custom_api"
    DATABASE = "database"
    CSV = "csv"

class PipelineStatus(enum.Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PROCESSING = "processing"

class RunStatus(enum.Enum):
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class InsightType(enum.Enum):
    ANOMALY = "anomaly"
    PREDICTION = "prediction"
    OPTIMIZATION = "optimization"
    TREND = "trend"

class InsightPriority(enum.Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class NotificationType(enum.Enum):
    SYSTEM = "system"
    AUTH = "auth"
    DATA = "data"
    ETL = "etl"
    AI_INSIGHT = "ai_insight"
    COLLABORATION = "collaboration"
    SECURITY = "security"
    PERFORMANCE = "performance"
    BILLING = "billing"
    USER_ACTION = "user_action"

class NotificationSeverity(enum.Enum):
    INFO = "info"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"

class NotificationStatus(enum.Enum):
    UNREAD = "unread"
    READ = "read"
    ARCHIVED = "archived"

# User Management Models
class User(Base):
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String, unique=True, index=True, nullable=False)
    username = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String)
    role = Column(SQLEnum(UserRole), default=UserRole.USER)
    is_active = Column(Boolean, default=True)
    is_verified = Column(Boolean, default=False)
    avatar_url = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    organizations = relationship("OrganizationUser", back_populates="user")
    dashboards = relationship("Dashboard", back_populates="owner")
    insights = relationship("AIInsight", back_populates="user")
    audit_logs = relationship("AuditLog", back_populates="user")
    notifications = relationship("Notification", back_populates="user", cascade="all, delete-orphan")

class Organization(Base):
    __tablename__ = "organizations"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    domain = Column(String)
    industry = Column(String)
    logo_url = Column(String)
    subscription_tier = Column(String)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    users = relationship("OrganizationUser", back_populates="organization")
    data_sources = relationship("DataSource", back_populates="organization")
    dashboards = relationship("Dashboard", back_populates="organization")
    notifications = relationship("Notification", back_populates="organization", cascade="all, delete-orphan")

class OrganizationUser(Base):
    __tablename__ = "organization_users"

    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    role = Column(SQLEnum(UserRole), default=UserRole.USER)
    joined_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    organization = relationship("Organization", back_populates="users")
    user = relationship("User", back_populates="organizations")

# Dashboard & Analytics Models
class Dashboard(Base):
    __tablename__ = "dashboards"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    owner_id = Column(Integer, ForeignKey("users.id"))
    is_public = Column(Boolean, default=False)
    is_template = Column(Boolean, default=False)
    template_category = Column(String)
    configuration = Column(JSON)  # Stores layout, widgets configuration
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    organization = relationship("Organization", back_populates="dashboards")
    owner = relationship("User", back_populates="dashboards")
    widgets = relationship("Widget", back_populates="dashboard")
    versions = relationship("DashboardVersion", back_populates="dashboard")

class Widget(Base):
    __tablename__ = "widgets"

    id = Column(Integer, primary_key=True, index=True)
    dashboard_id = Column(Integer, ForeignKey("dashboards.id"))
    widget_type = Column(String)  # chart, kpi, table, map, etc.
    title = Column(String)
    configuration = Column(JSON)  # Widget-specific config
    data_source_id = Column(Integer, ForeignKey("data_sources.id"))
    position = Column(JSON)  # x, y, width, height
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    dashboard = relationship("Dashboard", back_populates="widgets")
    data_source = relationship("DataSource")

# Data Management Models
class DataSource(Base):
    __tablename__ = "data_sources"

    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    name = Column(String, nullable=False)
    source_type = Column(SQLEnum(DataSourceType))
    connection_config = Column(JSON)  # Encrypted connection details
    is_active = Column(Boolean, default=True)
    last_sync = Column(DateTime(timezone=True))
    sync_frequency = Column(String)  # cron expression
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    organization = relationship("Organization", back_populates="data_sources")
    pipelines = relationship("ETLPipeline", back_populates="source")

class ETLPipeline(Base):
    __tablename__ = "etl_pipelines"

    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))  # Added missing field
    name = Column(String, nullable=False)
    description = Column(Text)
    source_id = Column(Integer, ForeignKey("data_sources.id"))
    destination_config = Column(JSON)
    transformation_rules = Column(JSON)
    schedule = Column(String)  # cron expression
    status = Column(SQLEnum(PipelineStatus), default=PipelineStatus.INACTIVE)
    last_run = Column(DateTime(timezone=True))
    next_run = Column(DateTime(timezone=True))
    records_processed = Column(Integer, default=0)
    success_rate = Column(Float, default=0.0)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())  # Added missing field

    # Relationships
    source = relationship("DataSource", back_populates="pipelines")
    runs = relationship("PipelineRun", back_populates="pipeline")

class PipelineRun(Base):
    __tablename__ = "pipeline_runs"

    id = Column(Integer, primary_key=True, index=True)
    pipeline_id = Column(Integer, ForeignKey("etl_pipelines.id"))
    status = Column(SQLEnum(RunStatus))
    started_at = Column(DateTime(timezone=True))
    finished_at = Column(DateTime(timezone=True))  # Changed from completed_at to match test/router
    records_processed = Column(Integer)
    error_message = Column(Text)
    run_metadata = Column(JSON)  # Added for additional run information

    # Relationships
    pipeline = relationship("ETLPipeline", back_populates="runs")

# AI & Insights Models
class AIInsight(Base):
    __tablename__ = "ai_insights"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    insight_type = Column(SQLEnum(InsightType))
    priority = Column(SQLEnum(InsightPriority))
    title = Column(String)
    description = Column(Text)
    confidence_score = Column(Float)
    data_source = Column(String)
    affected_metrics = Column(JSON)
    recommendations = Column(JSON)
    is_resolved = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    resolved_at = Column(DateTime(timezone=True))

    # Relationships
    user = relationship("User", back_populates="insights")

class PredictiveModel(Base):
    __tablename__ = "predictive_models"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    model_type = Column(String)  # demand_forecast, churn_prediction, etc.
    accuracy = Column(Float)
    confidence_threshold = Column(Float)
    features_used = Column(JSON)
    training_data_config = Column(JSON)
    last_trained = Column(DateTime(timezone=True))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

# Collaboration Models
class TeamWorkspace(Base):
    __tablename__ = "team_workspaces"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    members = relationship("WorkspaceMember", back_populates="workspace")
    comments = relationship("Comment", back_populates="workspace")

class WorkspaceMember(Base):
    __tablename__ = "workspace_members"

    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("team_workspaces.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    role = Column(String)
    joined_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    workspace = relationship("TeamWorkspace", back_populates="members")
    user = relationship("User")

class Comment(Base):
    __tablename__ = "comments"

    id = Column(Integer, primary_key=True, index=True)
    workspace_id = Column(Integer, ForeignKey("team_workspaces.id"))
    user_id = Column(Integer, ForeignKey("users.id"))
    widget_id = Column(Integer, ForeignKey("widgets.id"))
    content = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    workspace = relationship("TeamWorkspace", back_populates="comments")
    user = relationship("User")
    widget = relationship("Widget")

# Version Control
class DashboardVersion(Base):
    __tablename__ = "dashboard_versions"

    id = Column(Integer, primary_key=True, index=True)
    dashboard_id = Column(Integer, ForeignKey("dashboards.id"))
    version_number = Column(String)
    configuration = Column(JSON)
    change_description = Column(Text)
    created_by = Column(Integer, ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    dashboard = relationship("Dashboard", back_populates="versions")
    creator = relationship("User")

# Partner & White-label Models
class PartnerConfig(Base):
    __tablename__ = "partner_configs"

    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    custom_domain = Column(String)
    logo_url = Column(String)
    primary_color = Column(String)
    secondary_color = Column(String)
    custom_css = Column(Text)
    revenue_share_percentage = Column(Float)
    monthly_customers = Column(Integer)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

# Cost Management Models
class CostTracking(Base):
    __tablename__ = "cost_tracking"

    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    category = Column(String)  # processing, storage, api_calls
    amount = Column(Float)
    period = Column(DateTime(timezone=True))
    trend = Column(String)  # up, down, stable
    optimization_suggestions = Column(JSON)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

# Security & Compliance Models
class AuditLog(Base):
    __tablename__ = "audit_logs"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"))
    action = Column(String)
    resource_type = Column(String)
    resource_id = Column(Integer)
    ip_address = Column(String)
    user_agent = Column(String)
    action_metadata = Column(JSON)  # Changed from metadata to action_metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    user = relationship("User", back_populates="audit_logs")

class ComplianceReport(Base):
    __tablename__ = "compliance_reports"

    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    compliance_type = Column(String)  # GDPR, SOC2, HIPAA
    status = Column(String)  # compliant, non_compliant, partial
    findings = Column(JSON)
    recommendations = Column(JSON)
    generated_at = Column(DateTime(timezone=True), server_default=func.now())

# Metrics Models
class Metric(Base):
    __tablename__ = "metrics"

    id = Column(Integer, primary_key=True, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    name = Column(String)
    value = Column(Float)
    unit = Column(String)
    change_percentage = Column(Float)
    change_direction = Column(String)  # up, down, stable
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

# File Upload Models
class FileUpload(Base):
    __tablename__ = "file_uploads"

    id = Column(String, primary_key=True, index=True)  # UUID
    filename = Column(String, nullable=False)
    file_size = Column(Integer)
    file_type = Column(String)
    organization_id = Column(Integer, ForeignKey("organizations.id"))
    uploaded_by = Column(Integer, ForeignKey("users.id"))
    status = Column(String, default="pending")  # pending, processing, completed, error
    uploaded_at = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True))
    processing_result = Column(JSON)
    insights = Column(JSON)
    error_message = Column(Text)

    # Relationships
    organization = relationship("Organization")
    uploader = relationship("User")


# Notification System Models
class Notification(Base):
    __tablename__ = "notifications"

    id = Column(String, primary_key=True, index=True)  # UUID
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, index=True)
    organization_id = Column(Integer, ForeignKey("organizations.id"), nullable=True)
    type = Column(SQLEnum(NotificationType), nullable=False, index=True)
    severity = Column(SQLEnum(NotificationSeverity), nullable=False, default=NotificationSeverity.INFO)
    status = Column(SQLEnum(NotificationStatus), nullable=False, default=NotificationStatus.UNREAD, index=True)

    # Notification Content
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    details = Column(JSON)  # Additional structured data

    # Action and Context
    action_url = Column(String(500))  # URL for action button
    action_label = Column(String(100))  # Label for action button
    related_entity_type = Column(String(50))  # e.g., "etl_pipeline", "dashboard", "ai_insight"
    related_entity_id = Column(String(100))  # ID of the related entity

    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now(), index=True)
    read_at = Column(DateTime(timezone=True))
    archived_at = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))  # Auto-delete after this time

    # Notification Settings
    persistent = Column(Boolean, default=False)  # Whether notification stays until manually dismissed
    show_browser = Column(Boolean, default=True)  # Show browser notification
    play_sound = Column(Boolean, default=False)  # Play notification sound
    priority = Column(Integer, default=0)  # Higher priority notifications shown first

    # Relationships
    user = relationship("User", back_populates="notifications")
    organization = relationship("Organization", back_populates="notifications")