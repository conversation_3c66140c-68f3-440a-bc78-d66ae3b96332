"""
Encryption Service for secure credential storage
"""
from cryptography.fernet import <PERSON>rnet
import os
import base64
from typing import Optional


class EncryptionService:
    """Service for encrypting and decrypting sensitive data"""

    def __init__(self):
        # Get or generate encryption key
        encryption_key = os.environ.get("ENCRYPTION_KEY")
        if not encryption_key:
            # In development, warn about missing key
            import warnings
            warnings.warn("ENCRYPTION_KEY not found in environment. This is only acceptable in development.")
            # Generate temporary key (not persisted)
            encryption_key = Fernet.generate_key().decode()

        self.cipher = Fernet(encryption_key.encode() if isinstance(encryption_key, str) else encryption_key)

    def encrypt(self, data: str) -> str:
        """Encrypt a string and return base64 encoded encrypted data"""
        encrypted = self.cipher.encrypt(data.encode())
        return base64.b64encode(encrypted).decode()

    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt base64 encoded encrypted data and return original string"""
        decoded = base64.b64decode(encrypted_data.encode())
        decrypted = self.cipher.decrypt(decoded)
        return decrypted.decode()

    def encrypt_dict(self, data: dict) -> str:
        """Encrypt a dictionary as JSON string"""
        import json
        json_str = json.dumps(data)
        return self.encrypt(json_str)

    def decrypt_dict(self, encrypted_data: str) -> dict:
        """Decrypt and return dictionary from encrypted JSON string"""
        import json
        json_str = self.decrypt(encrypted_data)
        return json.loads(json_str)

    @staticmethod
    def generate_key() -> str:
        """Generate a new encryption key"""
        return Fernet.generate_key().decode()