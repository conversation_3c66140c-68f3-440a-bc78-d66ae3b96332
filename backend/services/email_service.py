"""
Email service for sending verification and notification emails
Supports both SMTP and development mode (console output)
"""

import os
import asyncio
import logging
from typing import Optional, Dict, Any
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import aiosmtplib
from jinja2 import Environment, FileSystemLoader, Template
from pathlib import Path

logger = logging.getLogger(__name__)


class EmailConfig:
    """Email configuration settings"""
    
    def __init__(self):
        self.smtp_host = os.getenv("SMTP_HOST", "smtp.gmail.com")
        self.smtp_port = int(os.getenv("SMTP_PORT", "587"))
        self.smtp_username = os.getenv("SMTP_USERNAME", "")
        self.smtp_password = os.getenv("SMTP_PASSWORD", "")
        self.smtp_use_tls = os.getenv("SMTP_USE_TLS", "true").lower() == "true"
        self.from_email = os.getenv("FROM_EMAIL", self.smtp_username)
        self.from_name = os.getenv("FROM_NAME", "DataFlow Pro")
        self.environment = os.getenv("ENVIRONMENT", "development")
        self.base_url = os.getenv("BASE_URL", "http://localhost:3000")
        
        # Template directory
        self.template_dir = Path(__file__).parent.parent / "templates" / "email"


class EmailService:
    """Service for sending emails with template support"""
    
    def __init__(self, config: Optional[EmailConfig] = None):
        self.config = config or EmailConfig()
        self.jinja_env = None
        self._setup_templates()
    
    def _setup_templates(self):
        """Setup Jinja2 template environment"""
        try:
            if self.config.template_dir.exists():
                self.jinja_env = Environment(
                    loader=FileSystemLoader(str(self.config.template_dir)),
                    autoescape=True
                )
            else:
                logger.warning(f"Email template directory not found: {self.config.template_dir}")
                # Create directory and basic templates
                self.config.template_dir.mkdir(parents=True, exist_ok=True)
                self._create_default_templates()
                self.jinja_env = Environment(
                    loader=FileSystemLoader(str(self.config.template_dir)),
                    autoescape=True
                )
        except Exception as e:
            logger.error(f"Failed to setup email templates: {e}")
            self.jinja_env = None
    
    def _create_default_templates(self):
        """Create default email templates"""
        templates = {
            "email_verification.html": """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Verify Your Email - {{ app_name }}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #007bff; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; background: #f8f9fa; }
        .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ app_name }}</h1>
        </div>
        <div class="content">
            <h2>Verify Your Email Address</h2>
            <p>Hello {{ user_name }},</p>
            <p>Thank you for registering with {{ app_name }}! To complete your registration, please verify your email address by clicking the button below:</p>
            <p style="text-align: center;">
                <a href="{{ verification_url }}" class="button">Verify Email Address</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 3px;">{{ verification_url }}</p>
            <p>This verification link will expire in 24 hours.</p>
            <p>If you didn't create an account with us, please ignore this email.</p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {{ app_name }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
            """,
            "password_reset.html": """
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Reset Your Password - {{ app_name }}</title>
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
        .content { padding: 30px 20px; background: #f8f9fa; }
        .button { display: inline-block; padding: 12px 24px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }
        .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{ app_name }}</h1>
        </div>
        <div class="content">
            <h2>Reset Your Password</h2>
            <p>Hello {{ user_name }},</p>
            <p>We received a request to reset your password for your {{ app_name }} account. Click the button below to reset your password:</p>
            <p style="text-align: center;">
                <a href="{{ reset_url }}" class="button">Reset Password</a>
            </p>
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background: #e9ecef; padding: 10px; border-radius: 3px;">{{ reset_url }}</p>
            <p>This password reset link will expire in 1 hour.</p>
            <p>If you didn't request a password reset, please ignore this email or contact support if you have concerns.</p>
        </div>
        <div class="footer">
            <p>&copy; 2024 {{ app_name }}. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
            """
        }
        
        for filename, content in templates.items():
            template_path = self.config.template_dir / filename
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(content.strip())
    
    def _render_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Render email template with context"""
        if not self.jinja_env:
            # Fallback to simple string replacement
            return self._render_fallback_template(template_name, context)
        
        try:
            template = self.jinja_env.get_template(template_name)
            return template.render(**context)
        except Exception as e:
            logger.error(f"Failed to render template {template_name}: {e}")
            return self._render_fallback_template(template_name, context)
    
    def _render_fallback_template(self, template_name: str, context: Dict[str, Any]) -> str:
        """Fallback template rendering without Jinja2"""
        if "verification" in template_name:
            return f"""
            <h2>Verify Your Email - {context.get('app_name', 'DataFlow Pro')}</h2>
            <p>Hello {context.get('user_name', 'User')},</p>
            <p>Please verify your email by clicking this link:</p>
            <p><a href="{context.get('verification_url', '#')}">Verify Email</a></p>
            <p>This link expires in 24 hours.</p>
            """
        elif "password" in template_name:
            return f"""
            <h2>Reset Your Password - {context.get('app_name', 'DataFlow Pro')}</h2>
            <p>Hello {context.get('user_name', 'User')},</p>
            <p>Click this link to reset your password:</p>
            <p><a href="{context.get('reset_url', '#')}">Reset Password</a></p>
            <p>This link expires in 1 hour.</p>
            """
        return f"<p>Email content for {template_name}</p>"
    
    async def send_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None,
        attachments: Optional[list] = None
    ) -> bool:
        """Send an email"""
        try:
            if self.config.environment == "development":
                return self._send_development_email(to_email, subject, html_content, text_content)
            
            return await self._send_smtp_email(to_email, subject, html_content, text_content, attachments)
        except Exception as e:
            logger.error(f"Failed to send email to {to_email}: {e}")
            return False
    
    def _send_development_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None
    ) -> bool:
        """Send email in development mode (console output)"""
        print("\n" + "="*60)
        print("📧 DEVELOPMENT EMAIL")
        print("="*60)
        print(f"To: {to_email}")
        print(f"From: {self.config.from_name} <{self.config.from_email}>")
        print(f"Subject: {subject}")
        print("-"*60)
        if text_content:
            print("TEXT CONTENT:")
            print(text_content)
            print("-"*60)
        print("HTML CONTENT:")
        print(html_content)
        print("="*60)
        return True

    async def _send_smtp_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        text_content: Optional[str] = None,
        attachments: Optional[list] = None
    ) -> bool:
        """Send email via SMTP"""
        if not self.config.smtp_username or not self.config.smtp_password:
            logger.error("SMTP credentials not configured")
            return False

        try:
            # Create message
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = f"{self.config.from_name} <{self.config.from_email}>"
            msg['To'] = to_email

            # Add text content
            if text_content:
                text_part = MIMEText(text_content, 'plain', 'utf-8')
                msg.attach(text_part)

            # Add HTML content
            html_part = MIMEText(html_content, 'html', 'utf-8')
            msg.attach(html_part)

            # Add attachments if any
            if attachments:
                for attachment in attachments:
                    self._add_attachment(msg, attachment)

            # Send email
            await aiosmtplib.send(
                msg,
                hostname=self.config.smtp_host,
                port=self.config.smtp_port,
                start_tls=self.config.smtp_use_tls,
                username=self.config.smtp_username,
                password=self.config.smtp_password,
            )

            logger.info(f"Email sent successfully to {to_email}")
            return True

        except Exception as e:
            logger.error(f"SMTP error sending email to {to_email}: {e}")
            return False

    def _add_attachment(self, msg: MIMEMultipart, attachment: Dict[str, Any]):
        """Add attachment to email message"""
        try:
            filename = attachment.get('filename', 'attachment')
            content = attachment.get('content', b'')
            content_type = attachment.get('content_type', 'application/octet-stream')

            part = MIMEBase(*content_type.split('/'))
            part.set_payload(content)
            encoders.encode_base64(part)
            part.add_header(
                'Content-Disposition',
                f'attachment; filename= {filename}'
            )
            msg.attach(part)
        except Exception as e:
            logger.error(f"Failed to add attachment {attachment.get('filename', 'unknown')}: {e}")

    async def send_verification_email(self, user_email: str, user_name: str, verification_token: str) -> bool:
        """Send email verification email"""
        verification_url = f"{self.config.base_url}/verify-email?token={verification_token}"

        context = {
            'app_name': 'DataFlow Pro',
            'user_name': user_name or user_email.split('@')[0],
            'verification_url': verification_url,
            'base_url': self.config.base_url
        }

        html_content = self._render_template('email_verification.html', context)
        text_content = f"""
Hello {context['user_name']},

Thank you for registering with DataFlow Pro!

To complete your registration, please verify your email address by visiting:
{verification_url}

This verification link will expire in 24 hours.

If you didn't create an account with us, please ignore this email.

Best regards,
The DataFlow Pro Team
        """.strip()

        return await self.send_email(
            to_email=user_email,
            subject="Verify Your Email - DataFlow Pro",
            html_content=html_content,
            text_content=text_content
        )

    async def send_password_reset_email(self, user_email: str, user_name: str, reset_token: str) -> bool:
        """Send password reset email"""
        reset_url = f"{self.config.base_url}/reset-password?token={reset_token}"

        context = {
            'app_name': 'DataFlow Pro',
            'user_name': user_name or user_email.split('@')[0],
            'reset_url': reset_url,
            'base_url': self.config.base_url
        }

        html_content = self._render_template('password_reset.html', context)
        text_content = f"""
Hello {context['user_name']},

We received a request to reset your password for your DataFlow Pro account.

To reset your password, please visit:
{reset_url}

This password reset link will expire in 1 hour.

If you didn't request a password reset, please ignore this email or contact support if you have concerns.

Best regards,
The DataFlow Pro Team
        """.strip()

        return await self.send_email(
            to_email=user_email,
            subject="Reset Your Password - DataFlow Pro",
            html_content=html_content,
            text_content=text_content
        )

    async def send_welcome_email(self, user_email: str, user_name: str) -> bool:
        """Send welcome email after successful verification"""
        context = {
            'app_name': 'DataFlow Pro',
            'user_name': user_name or user_email.split('@')[0],
            'base_url': self.config.base_url,
            'dashboard_url': f"{self.config.base_url}/dashboard"
        }

        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Welcome to DataFlow Pro</title>
    <style>
        body {{ font-family: Arial, sans-serif; line-height: 1.6; color: #333; }}
        .container {{ max-width: 600px; margin: 0 auto; padding: 20px; }}
        .header {{ background: #28a745; color: white; padding: 20px; text-align: center; }}
        .content {{ padding: 30px 20px; background: #f8f9fa; }}
        .button {{ display: inline-block; padding: 12px 24px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; margin: 20px 0; }}
        .footer {{ padding: 20px; text-align: center; color: #666; font-size: 12px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Welcome to DataFlow Pro!</h1>
        </div>
        <div class="content">
            <h2>Your account is ready!</h2>
            <p>Hello {context['user_name']},</p>
            <p>Welcome to DataFlow Pro! Your email has been verified and your account is now active.</p>
            <p>You can now access all features of our enterprise analytics platform:</p>
            <ul>
                <li>📊 Create interactive dashboards</li>
                <li>🔄 Build ETL pipelines</li>
                <li>🤖 Get AI-powered insights</li>
                <li>👥 Collaborate with your team</li>
            </ul>
            <p style="text-align: center;">
                <a href="{context['dashboard_url']}" class="button">Go to Dashboard</a>
            </p>
            <p>If you have any questions, feel free to contact our support team.</p>
        </div>
        <div class="footer">
            <p>&copy; 2024 DataFlow Pro. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
        """

        text_content = f"""
Welcome to DataFlow Pro!

Hello {context['user_name']},

Your email has been verified and your account is now active.

You can now access all features of our enterprise analytics platform:
- Create interactive dashboards
- Build ETL pipelines
- Get AI-powered insights
- Collaborate with your team

Visit your dashboard: {context['dashboard_url']}

If you have any questions, feel free to contact our support team.

Best regards,
The DataFlow Pro Team
        """.strip()

        return await self.send_email(
            to_email=user_email,
            subject="Welcome to DataFlow Pro - Your Account is Ready!",
            html_content=html_content,
            text_content=text_content
        )


# Global email service instance
email_service = EmailService()
