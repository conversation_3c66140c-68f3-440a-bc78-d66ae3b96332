// Test script to verify frontend registration flow
const axios = require('axios');

async function testFrontendRegistration() {
    console.log('🧪 Testing Frontend Registration Flow');
    console.log('=' * 50);

    // Test the exact same request that the frontend would make
    const frontendRequest = {
        email: '<EMAIL>',
        username: 'frontendtest',
        full_name: 'Frontend Test User',
        password: 'TestPass123!'
    };

    try {
        console.log('📤 Sending frontend-style registration request...');
        console.log('Request data:', frontendRequest);

        const response = await axios.post(
            'http://localhost:8000/api/v1/auth/register',
            frontendRequest,
            {
                headers: {
                    'Content-Type': 'application/json'
                },
                withCredentials: false
            }
        );

        console.log('✅ Registration successful!');
        console.log('Response status:', response.status);
        console.log('Response data:', response.data);

        // Test the signUp mapping
        const signUpData = {
            firstName: 'Frontend',
            lastName: 'Test User',
            email: '<EMAIL>',
            phone: '+1234567890',
            organizationName: 'Test Org',
            password: 'TestPass123!'
        };

        // Map to register format (same as frontend does)
        const mappedData = {
            email: signUpData.email,
            username: signUpData.email.split('@')[0], // Use email prefix as username
            full_name: `${signUpData.firstName} ${signUpData.lastName}`,
            password: signUpData.password
        };

        console.log('\n📤 Testing signUp mapping...');
        console.log('Original signUp data:', signUpData);
        console.log('Mapped register data:', mappedData);

        const signUpResponse = await axios.post(
            'http://localhost:8000/api/v1/auth/register',
            mappedData,
            {
                headers: {
                    'Content-Type': 'application/json'
                },
                withCredentials: false
            }
        );

        console.log('✅ SignUp mapping successful!');
        console.log('Response status:', signUpResponse.status);
        console.log('Response data:', signUpResponse.data);

    } catch (error) {
        console.log('❌ Registration failed');
        console.log('Error status:', error.response?.status);
        console.log('Error data:', error.response?.data);
        console.log('Error message:', error.message);

        if (error.response?.status === 400 && error.response?.data?.detail?.includes('already')) {
            console.log('ℹ️  User already exists - this is expected for repeated tests');
        }
    }

    console.log('\n✅ Frontend registration test completed!');
}

// Only run if axios is available
try {
    require('axios');
    testFrontendRegistration().catch(console.error);
} catch (e) {
    console.log('❌ axios not available. Install with: npm install axios');
}
