<!DOCTYPE html>
<html>
<head>
    <title>Test Registration</title>
</head>
<body>
    <h1>Test Registration</h1>
    <form id="registrationForm">
        <div>
            <label>Email:</label>
            <input type="email" id="email" value="<EMAIL>" required>
        </div>
        <div>
            <label>Username:</label>
            <input type="text" id="username" value="testuser" required>
        </div>
        <div>
            <label>Full Name:</label>
            <input type="text" id="fullName" value="Test User" required>
        </div>
        <div>
            <label>Password:</label>
            <input type="password" id="password" value="TestPass123!" required>
        </div>
        <button type="submit">Register</button>
    </form>

    <div id="result"></div>

    <script>
        document.getElementById('registrationForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = {
                email: document.getElementById('email').value,
                username: document.getElementById('username').value,
                full_name: document.getElementById('fullName').value,
                password: document.getElementById('password').value
            };

            console.log('Sending registration data:', formData);

            try {
                const response = await fetch('http://localhost:8000/api/v1/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                console.log('Response status:', response.status);
                const responseText = await response.text();
                console.log('Response text:', responseText);

                if (response.ok) {
                    document.getElementById('result').innerHTML = 
                        '<div style="color: green;">✅ Registration successful!</div>' +
                        '<pre>' + responseText + '</pre>';
                } else {
                    document.getElementById('result').innerHTML = 
                        '<div style="color: red;">❌ Registration failed</div>' +
                        '<pre>' + responseText + '</pre>';
                }
            } catch (error) {
                console.error('Registration error:', error);
                document.getElementById('result').innerHTML = 
                    '<div style="color: red;">❌ Network error: ' + error.message + '</div>';
            }
        });
    </script>
</body>
</html>
