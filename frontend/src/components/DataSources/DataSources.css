.data-sources-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.data-sources-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.data-sources-header h2 {
  margin: 0;
  color: #333;
}

.error-message {
  background-color: #fee;
  color: #c00;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1rem;
  border: 1px solid #fcc;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  background: #f8f9fa;
  border-radius: 8px;
  color: #666;
}

.empty-state h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

.data-sources-list {
  display: grid;
  gap: 1.5rem;
}

.data-source-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s;
}

.data-source-card:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.data-source-info {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 1rem;
}

.data-source-info h3 {
  margin: 0;
  color: #333;
  flex: 1;
}

.source-type {
  color: #666;
  font-size: 0.9rem;
  text-transform: capitalize;
  margin: 0;
}

.status-indicator {
  padding: 0.25rem 0.75rem;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-connected {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status-retrying {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-paused {
  background-color: #e9ecef;
  color: #495057;
  border: 1px solid #dee2e6;
}

.data-source-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.btn {
  padding: 0.5rem 1rem;
  border: 1px solid #ccc;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s;
}

.btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #999;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  border-color: #0056b3;
}

.btn-danger {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
  border-color: #bd2130;
}

.btn-secondary {
  background: #6c757d;
  color: white;
  border-color: #6c757d;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
  border-color: #545b62;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.85rem;
}

.delete-confirm {
  margin-top: 1rem;
  padding: 1rem;
  background: #ffeee0;
  border-radius: 4px;
  border: 1px solid #ffcc99;
}

.delete-confirm p {
  margin-bottom: 1rem;
  color: #721c24;
}

.delete-confirm .btn {
  margin-right: 0.5rem;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 2rem;
  max-width: 900px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  color: #333;
}

.connector-category {
  margin-bottom: 2rem;
}

.category-title {
  color: #667eea;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
  border-bottom: 2px solid #f0f0f0;
  padding-bottom: 0.5rem;
}

.provider-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.provider-card {
  padding: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  transition: all 0.3s;
}

.provider-card:hover {
  background: #f8f9fa;
  border-color: #007bff;
}

.provider-card h4 {
  margin: 0 0 0.5rem 0;
  color: #333;
}

.provider-card p {
  margin: 0 0 1rem 0;
  color: #666;
  font-size: 0.9rem;
}

.provider-card .btn {
  width: 100%;
}