{"name": "frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.3.2", "@mui/material": "^7.3.2", "@mui/system": "^7.3.2", "@mui/x-data-grid": "^8.11.3", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/react-router-dom": "^5.3.3", "axios": "^1.12.2", "chart.js": "^4.5.0", "date-fns": "^4.1.0", "framer-motion": "^12.23.16", "lucide-react": "^0.544.0", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-dom": "^19.1.1", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.9.1", "react-scripts": "5.0.1", "recharts": "^3.2.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"transformIgnorePatterns": ["node_modules/(?!(axios)/)"]}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}}