#!/usr/bin/env python3
"""
<PERSON>ript to send DataFlow Pro brochure via email
Note: This script shows how to send the email.
You'll need to configure your email settings or use your email client.
"""

import os
from datetime import datetime

# Email details
recipient = "<EMAIL>"
subject = "DataFlow Pro - Transform Your Business Intelligence [Brochure Attached]"
pdf_path = os.path.expanduser("~/Desktop/DataFlow_Pro_Brochure.pdf")

# Email body
email_body = """
Dear Business Leader,

I'm excited to share with you DataFlow Pro - a revolutionary enterprise analytics platform that's helping businesses transform their data into strategic advantage.

DataFlow Pro solves the critical challenge that most businesses face today: data scattered across multiple systems with no unified view. Our platform brings everything together in one place, providing:

✅ Real-time dashboards that update 24/7
✅ AI-powered insights that predict issues before they happen
✅ Connections to 100+ data sources (Salesforce, HubSpot, QuickBooks, Stripe, and more)
✅ Team collaboration features for aligned decision-making
✅ Enterprise-grade security with GDPR/SOC2 compliance

What makes DataFlow Pro special:
• 87% faster reporting
• 3.5x ROI in just 6 months
• 42% average revenue increase for our clients

I've attached a comprehensive brochure that details:
- How DataFlow Pro solves your data challenges
- Current and upcoming features
- Real success stories from businesses like yours
- Simple, transparent pricing options
- Why now is the perfect time to get started (early adopter benefits!)

Ready to see DataFlow Pro in action? Start your free 14-day trial today:
• No credit card required
• Setup in 5 minutes
• Full access to all features
• White-glove onboarding support

Let's transform your business intelligence together!

Best regards,
The DataFlow Pro Team

P.S. Early adopters lock in lifetime pricing - prices increase in January 2025!

---
📧 <EMAIL>
📞 1-800-DATA-FLO
💬 Live Chat: dataflowpro.com/chat
🌐 Website: dataflowpro.com
"""

print("=" * 60)
print("EMAIL READY TO SEND")
print("=" * 60)
print(f"\nTo: {recipient}")
print(f"Subject: {subject}")
print(f"Attachment: {pdf_path}")
print(f"\nEmail Body Preview:\n{'-' * 40}")
print(email_body[:500] + "...")
print("-" * 40)
print("\n📧 SENDING OPTIONS:")
print("-" * 40)

# Check if file exists
if os.path.exists(pdf_path):
    file_size = os.path.getsize(pdf_path) / (1024 * 1024)  # Convert to MB
    print(f"✅ PDF Found: {pdf_path}")
    print(f"   Size: {file_size:.1f} MB")
else:
    print(f"❌ PDF not found at: {pdf_path}")

print("\n🚀 HOW TO SEND THIS EMAIL:")
print("-" * 40)
print("\nOPTION 1: Open your email client (Gmail, Outlook, Apple Mail)")
print("  1. Create new email to: <EMAIL>")
print("  2. Copy the subject line above")
print("  3. Copy the email body text above")
print("  4. Attach: ~/Desktop/DataFlow_Pro_Brochure.pdf")
print("  5. Send!")

print("\nOPTION 2: Use Apple Mail from Terminal (Mac):")
print(f'  open -a Mail "mailto:{recipient}?subject={subject.replace(" ", "%20")}"')
print("  Then attach the PDF manually")

print("\nOPTION 3: Use Gmail web interface:")
print("  1. Go to https://mail.google.com")
print("  2. Click 'Compose'")
print(f"  3. To: {recipient}")
print("  4. Copy subject and body from above")
print("  5. Click paperclip icon to attach PDF")

print("\n" + "=" * 60)
print("EMAIL TEMPLATE CREATED SUCCESSFULLY!")
print("=" * 60)

# Create a text file with the email content for easy copying
email_file = os.path.expanduser("~/Desktop/DataFlow_Pro_Email.txt")
with open(email_file, 'w') as f:
    f.write(f"To: {recipient}\n")
    f.write(f"Subject: {subject}\n")
    f.write(f"Attachment: {pdf_path}\n")
    f.write(f"\n{'=' * 60}\n")
    f.write("EMAIL BODY:\n")
    f.write(f"{'=' * 60}\n\n")
    f.write(email_body)

print(f"\n📝 Email template saved to: {email_file}")
print("   (You can copy/paste from this file)")