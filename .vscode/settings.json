{"python.defaultInterpreterPath": "./backend/.venv/bin/python", "python.terminal.activateEnvironment": true, "python.analysis.extraPaths": ["./backend"], "python.analysis.include": ["./backend/**"], "python.analysis.exclude": ["./backend/.venv/**", "./backend/.uv-cache/**", "./backend/venv/**", "./backend/__pycache__/**"], "python.analysis.autoSearchPaths": true, "python.analysis.autoImportCompletions": true, "python.analysis.typeCheckingMode": "basic", "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "python.formatting.provider": "black", "files.associations": {"*.py": "python"}, "python.envFile": "./backend/.env"}