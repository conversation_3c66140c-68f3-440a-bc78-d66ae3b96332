<!DOCTYPE html>
<html>
<head>
    <title>Standalone File Upload Test</title>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }
        #result {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ccc;
            background: #f5f5f5;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>Standalone File Upload Test</h1>

    <h2>Method 1: Vanilla JavaScript Fetch</h2>
    <input type="file" id="fetchFileInput" />
    <button onclick="uploadWithFetch()">Upload with Fetch</button>

    <h2>Method 2: Axios from CDN</h2>
    <input type="file" id="axiosFileInput" />
    <button onclick="uploadWithAxios()">Upload with Axios</button>

    <h2>Method 3: XMLHttpRequest</h2>
    <input type="file" id="xhrFileInput" />
    <button onclick="uploadWithXHR()">Upload with XHR</button>

    <div id="result"></div>

    <script>
        const apiUrl = 'http://localhost:8000/api/v1/files/upload';
        const resultDiv = document.getElementById('result');

        function log(message, isError = false) {
            console.log(message);
            resultDiv.innerHTML += `${new Date().toLocaleTimeString()} - ${message}\n`;
            resultDiv.className = isError ? 'error' : 'success';
        }

        // Method 1: Vanilla JavaScript Fetch
        async function uploadWithFetch() {
            const fileInput = document.getElementById('fetchFileInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('Please select a file');
                return;
            }

            resultDiv.innerHTML = '';
            log('Starting Fetch upload...');

            const formData = new FormData();
            formData.append('file', file);

            try {
                log(`Uploading ${file.name} (${file.size} bytes) using Fetch...`);

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    body: formData
                });

                log(`Response status: ${response.status}`);

                const data = await response.json();
                log('SUCCESS with Fetch!');
                log(JSON.stringify(data, null, 2));
            } catch (error) {
                log(`ERROR with Fetch: ${error.message}`, true);
                console.error('Fetch error:', error);
            }
        }

        // Method 2: Axios from CDN
        async function uploadWithAxios() {
            const fileInput = document.getElementById('axiosFileInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('Please select a file');
                return;
            }

            resultDiv.innerHTML = '';
            log('Starting Axios upload...');

            const formData = new FormData();
            formData.append('file', file);

            try {
                log(`Uploading ${file.name} (${file.size} bytes) using Axios...`);

                const response = await axios.post(apiUrl, formData, {
                    onUploadProgress: (progressEvent) => {
                        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                        log(`Upload progress: ${percentCompleted}%`);
                    }
                });

                log('SUCCESS with Axios!');
                log(JSON.stringify(response.data, null, 2));
            } catch (error) {
                log(`ERROR with Axios: ${error.message}`, true);
                if (error.response) {
                    log(`Response status: ${error.response.status}`, true);
                    log(`Response data: ${JSON.stringify(error.response.data)}`, true);
                } else if (error.request) {
                    log('No response received', true);
                    console.error('Request:', error.request);
                } else {
                    log(`Setup error: ${error.message}`, true);
                }
            }
        }

        // Method 3: XMLHttpRequest
        function uploadWithXHR() {
            const fileInput = document.getElementById('xhrFileInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('Please select a file');
                return;
            }

            resultDiv.innerHTML = '';
            log('Starting XMLHttpRequest upload...');

            const formData = new FormData();
            formData.append('file', file);

            const xhr = new XMLHttpRequest();

            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    log(`Upload progress: ${percentComplete.toFixed(0)}%`);
                }
            });

            xhr.addEventListener('load', function() {
                if (xhr.status === 200) {
                    log('SUCCESS with XMLHttpRequest!');
                    log(xhr.responseText);
                } else {
                    log(`ERROR: Status ${xhr.status}`, true);
                }
            });

            xhr.addEventListener('error', function() {
                log('ERROR with XMLHttpRequest!', true);
            });

            xhr.open('POST', apiUrl);
            xhr.send(formData);
        }

        log('Test page ready. Select a file and click one of the upload buttons.');
    </script>
</body>
</html>